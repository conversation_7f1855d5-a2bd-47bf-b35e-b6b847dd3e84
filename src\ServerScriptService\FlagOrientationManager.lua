-- FlagOrientationManager.lua
-- Orienta as bandeiras das bases para apontar para o centro do mapa

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))

local FlagOrientationManager = {}

-- Função para orientar uma bandeira para o centro do mapa
local function orientFlagToCenter(flag, basePosition)
    if not flag or not basePosition then return end
    
    -- Centro do mapa onde está o botão arena central backup
    local mapCenter = Vector3.new(0, 0, 0)
    
    -- Calcula a direção da base para o centro
    local direction = (mapCenter - basePosition).Unit
    
    -- Calcula o ângulo de rotação no eixo Y para que a frente da bandeira aponte para o centro
    local angle = math.atan2(-direction.X, -direction.Z) -- Negativo para que a frente aponte para o centro
    
    -- Aplica a rotação à bandeira
    local cf = CFrame.new(flag.Position) * CFrame.Angles(0, angle, 0)
    flag.CFrame = cf
    
    print("Bandeira orientada para o centro do mapa. Base em:", basePosition, "Ângulo:", math.deg(angle))
end

-- Função para orientar todas as bandeiras existentes
function FlagOrientationManager.OrientAllFlags()
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local coreTower = base:FindFirstChild("CoreTower")
            if coreTower then
                local flag = coreTower:FindFirstChild("Flag")
                if flag then
                    -- Usa a posição da base (CoreTower) como referência
                    orientFlagToCenter(flag, coreTower.Position)
                end
            end
        end
    end
end

-- Função para orientar uma bandeira específica quando uma base é criada
function FlagOrientationManager.OrientFlag(baseModel)
    if not baseModel then return end
    
    local coreTower = baseModel:FindFirstChild("CoreTower")
    if not coreTower then return end
    
    local flag = coreTower:FindFirstChild("Flag")
    if not flag then return end
    
    -- Usa a posição da CoreTower como referência
    orientFlagToCenter(flag, coreTower.Position)
end

-- Conecta ao evento de criação de bases
workspace.ChildAdded:Connect(function(child)
    if child.Name:match("Base_") then
        -- Aguarda um pouco para garantir que todos os componentes foram criados
        wait(0.1)
        FlagOrientationManager.OrientFlag(child)
    end
end)

-- Orienta todas as bandeiras existentes ao inicializar (incluindo bases já no workspace)
spawn(function()
    wait(1) -- Aguarda o jogo carregar
    FlagOrientationManager.OrientAllFlags()
    print("✅ Todas as bandeiras orientadas para o centro do mapa")
end)

print("FlagOrientationManager inicializado com sucesso!")

return FlagOrientationManager