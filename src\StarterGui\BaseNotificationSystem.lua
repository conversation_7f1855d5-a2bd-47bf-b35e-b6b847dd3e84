-- BaseNotificationSystem.lua
-- Sistema de notificações para bases (cura e perigo)

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:Wait<PERSON><PERSON><PERSON>hild("PlayerGui")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local showBaseNotification = remoteEvents:WaitForChild("ShowBaseNotification")
local hideBaseNotification = remoteEvents:WaitForChild("HideBaseNotification")
local showHealEffect = remoteEvents:WaitForChild("ShowHealEffect")

-- Variáveis globais
local currentNotificationGui = nil
local healEffectGui = nil

-- Função para criar notificação de base
local function createBaseNotification(title, message, color)
    -- Remove notificação existente
    if currentNotificationGui then
        currentNotificationGui:Destroy()
    end
    
    -- Cria nova GUI
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "BaseNotificationGUI"
    screenGui.Parent = playerGui
    
    -- Frame principal
    local notificationFrame = Instance.new("Frame")
    notificationFrame.Size = UDim2.new(0, 300, 0, 80)
    notificationFrame.Position = UDim2.new(0.5, -150, 0, -100) -- Começa fora da tela
    notificationFrame.BackgroundColor3 = color
    notificationFrame.BackgroundTransparency = 0.1
    notificationFrame.BorderSizePixel = 3
    notificationFrame.BorderColor3 = Color3.new(1, 1, 1)
    notificationFrame.Parent = screenGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = notificationFrame
    
    -- Título
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0.5, 0)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = title
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = notificationFrame
    
    -- Mensagem
    local messageLabel = Instance.new("TextLabel")
    messageLabel.Size = UDim2.new(1, 0, 0.5, 0)
    messageLabel.Position = UDim2.new(0, 0, 0.5, 0)
    messageLabel.BackgroundTransparency = 1
    messageLabel.Text = message
    messageLabel.TextColor3 = Color3.new(0.9, 0.9, 0.9)
    messageLabel.TextScaled = true
    messageLabel.Font = Enum.Font.Gotham
    messageLabel.Parent = notificationFrame
    
    -- Efeito de entrada
    local tweenInfo = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
    local tween = TweenService:Create(notificationFrame, tweenInfo, {
        Position = UDim2.new(0.5, -150, 0, 20)
    })
    tween:Play()
    
    -- Efeito de pulsação para notificações de perigo
    if color == Color3.new(1, 0, 0) then
        local pulseConnection = RunService.Heartbeat:Connect(function()
            if notificationFrame and notificationFrame.Parent then
                local time = tick()
                local alpha = (math.sin(time * 8) + 1) / 2 -- Oscila entre 0 e 1
                notificationFrame.BackgroundTransparency = 0.1 + (alpha * 0.3)
            else
                pulseConnection:Disconnect()
            end
        end)
    end
    
    currentNotificationGui = screenGui
    return screenGui
end

-- Função para esconder notificação
local function hideNotification()
    if currentNotificationGui then
        local notificationFrame = currentNotificationGui:FindFirstChild("Frame")
        if notificationFrame then
            -- Efeito de saída
            local tweenInfo = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.In)
            local tween = TweenService:Create(notificationFrame, tweenInfo, {
                Position = UDim2.new(0.5, -150, 0, -100),
                BackgroundTransparency = 1
            })
            tween:Play()
            
            tween.Completed:Connect(function()
                if currentNotificationGui then
                    currentNotificationGui:Destroy()
                    currentNotificationGui = nil
                end
            end)
        else
            currentNotificationGui:Destroy()
            currentNotificationGui = nil
        end
    end
end

-- Função para criar efeito de cura
local function createHealEffect()
    -- Remove efeito existente
    if healEffectGui then
        healEffectGui:Destroy()
    end
    
    -- Cria nova GUI
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "HealEffectGUI"
    screenGui.Parent = playerGui
    
    -- Frame do efeito
    local effectFrame = Instance.new("Frame")
    effectFrame.Size = UDim2.new(0, 100, 0, 30)
    effectFrame.Position = UDim2.new(0.5, -50, 0.3, 0)
    effectFrame.BackgroundColor3 = Color3.new(0, 0.8, 0)
    effectFrame.BackgroundTransparency = 0.3
    effectFrame.BorderSizePixel = 2
    effectFrame.BorderColor3 = Color3.new(0, 1, 0)
    effectFrame.Parent = screenGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = effectFrame
    
    -- Texto de cura
    local healText = Instance.new("TextLabel")
    healText.Size = UDim2.new(1, 0, 1, 0)
    healText.BackgroundTransparency = 1
    healText.Text = "💚 +CURA"
    healText.TextColor3 = Color3.new(1, 1, 1)
    healText.TextScaled = true
    healText.Font = Enum.Font.SourceSansBold
    healText.Parent = effectFrame
    
    -- Efeito de movimento para cima e fade
    local tweenInfo = TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    local tween = TweenService:Create(effectFrame, tweenInfo, {
        Position = UDim2.new(0.5, -50, 0.2, 0),
        BackgroundTransparency = 1
    })
    
    local textTween = TweenService:Create(healText, tweenInfo, {
        TextTransparency = 1
    })
    
    tween:Play()
    textTween:Play()
    
    tween.Completed:Connect(function()
        screenGui:Destroy()
    end)
    
    healEffectGui = screenGui
end

-- Conecta eventos
showBaseNotification.OnClientEvent:Connect(function(title, message, color)
    createBaseNotification(title, message, color)
end)

hideBaseNotification.OnClientEvent:Connect(function()
    hideNotification()
end)

showHealEffect.OnClientEvent:Connect(function()
    createHealEffect()
end)

print("BaseNotificationSystem carregado com sucesso!")