-- EnhancedBuildingManager.lua
-- Sistema de construção melhorado com mais tipos de construções

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local openBuildMenu = remoteEvents:WaitForChild("OpenBuildMenu")
local buildStructure = remoteEvents:WaitForChild("BuildStructure")
local moveStructure = remoteEvents:WaitForChild("MoveStructure")
local deleteStructure = remoteEvents:WaitForChild("DeleteStructure")
local updateBaseInfo = remoteEvents:WaitForChild("UpdateBaseInfo")

local EnhancedBuildingManager = {}

-- Tipos de construções expandidos
local buildingTypes = {
    {
        name = "DefenseTurret",
        displayName = "Torre de Defesa",
        description = "Ataca inimigos automaticamente dentro do alcance",
        cost = 50,
        size = Vector3.new(3, 6, 3),
        color = BrickColor.new("Dark stone grey"),
        material = Enum.Material.Concrete
    },
    {
        name = "ResourceGenerator",
        displayName = "Gerador de Recursos",
        description = "Gera recursos automaticamente ao longo do tempo",
        cost = 75,
        size = Vector3.new(4, 3, 4),
        color = BrickColor.new("Bright blue"),
        material = Enum.Material.Neon
    },
    {
        name = "ShieldGenerator",
        displayName = "Gerador de Escudo",
        description = "Fornece proteção extra para a base",
        cost = 100,
        size = Vector3.new(2, 8, 2),
        color = BrickColor.new("Cyan"),
        material = Enum.Material.ForceField
    },
    {
        name = "HealthStation",
        displayName = "Estação de Cura",
        description = "Cura jogadores aliados próximos mais rapidamente",
        cost = 60,
        size = Vector3.new(3, 4, 3),
        color = BrickColor.new("Lime green"),
        material = Enum.Material.Neon
    },
    {
        name = "WatchTower",
        displayName = "Torre de Observação",
        description = "Aumenta o alcance de detecção de inimigos",
        cost = 40,
        size = Vector3.new(2, 12, 2),
        color = BrickColor.new("Brown"),
        material = Enum.Material.Wood
    },
    {
        name = "SupplyDepot",
        displayName = "Depósito de Suprimentos",
        description = "Aumenta a capacidade de armazenamento da base",
        cost = 80,
        size = Vector3.new(5, 3, 5),
        color = BrickColor.new("Reddish brown"),
        material = Enum.Material.Wood
    },
    {
        name = "EnergyCore",
        displayName = "Núcleo de Energia",
        description = "Alimenta outras construções e melhora eficiência",
        cost = 120,
        size = Vector3.new(3, 5, 3),
        color = BrickColor.new("New Yeller"),
        material = Enum.Material.Neon
    },
    {
        name = "Barricade",
        displayName = "Barricada",
        description = "Bloqueia movimento de inimigos e fornece cobertura",
        cost = 25,
        size = Vector3.new(6, 2, 1),
        color = BrickColor.new("Dark stone grey"),
        material = Enum.Material.Concrete
    }
}

-- Função para verificar se jogador pode construir
local function canPlayerBuild(player, buildingType, position)
    -- Verifica se o jogador tem uma base
    local playerBase = nil
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                playerBase = base
                break
            end
        end
    end
    
    if not playerBase then
        return false, "Você não possui uma base!"
    end
    
    -- Verifica materiais
    local buildingMaterials = playerBase:FindFirstChild("BuildingMaterials")
    if not buildingMaterials or buildingMaterials.Value < buildingType.cost then
        return false, "Materiais insuficientes! Necessário: " .. buildingType.cost
    end
    
    -- Verifica se a posição está dentro da base
    local barrier = playerBase:FindFirstChild("Barrier")
    if barrier then
        local distance = (position - barrier.Position).Magnitude
        local barrierRadius = barrier.Size.X / 2
        
        if distance > barrierRadius - 5 then -- 5 studs de margem
            return false, "Construção deve estar dentro da área da base!"
        end
    end
    
    return true, playerBase
end

-- Função para criar construção
local function createBuilding(buildingType, position, base)
    local building = Instance.new("Part")
    building.Name = buildingType.name .. "_Building"
    building.Size = buildingType.size
    building.Position = position + Vector3.new(0, buildingType.size.Y/2, 0)
    building.BrickColor = buildingType.color
    building.Material = buildingType.material
    building.CanCollide = true
    building.Anchored = true
    building.Parent = workspace
    
    -- Adiciona referência à base
    local baseOwner = Instance.new("ObjectValue")
    baseOwner.Name = "BaseOwner"
    baseOwner.Value = base
    baseOwner.Parent = building
    
    -- Adiciona informações da construção
    local buildingInfo = Instance.new("StringValue")
    buildingInfo.Name = "BuildingType"
    buildingInfo.Value = buildingType.name
    buildingInfo.Parent = building
    
    -- Adiciona efeitos especiais baseados no tipo
    if buildingType.name == "DefenseTurret" then
        -- Adiciona canhão no topo
        local cannon = Instance.new("Part")
        cannon.Name = "Cannon"
        cannon.Size = Vector3.new(0.5, 0.5, 2)
        cannon.Position = building.Position + Vector3.new(0, building.Size.Y/2 + 0.25, 0)
        cannon.BrickColor = BrickColor.new("Really black")
        cannon.Material = Enum.Material.Metal
        cannon.CanCollide = false
        cannon.Anchored = true
        cannon.Parent = building
        
    elseif buildingType.name == "ResourceGenerator" then
        -- Adiciona efeito de energia
        local light = Instance.new("PointLight")
        light.Color = Color3.new(0, 0, 1)
        light.Brightness = 2
        light.Range = 10
        light.Parent = building
        
    elseif buildingType.name == "ShieldGenerator" then
        -- Adiciona efeito de escudo
        local shield = Instance.new("Part")
        shield.Name = "ShieldEffect"
        shield.Size = Vector3.new(6, 6, 6)
        shield.Position = building.Position
        shield.BrickColor = BrickColor.new("Cyan")
        shield.Material = Enum.Material.ForceField
        shield.CanCollide = false
        shield.Anchored = true
        shield.Transparency = 0.8
        shield.Shape = Enum.PartType.Ball
        shield.Parent = building
        
    elseif buildingType.name == "HealthStation" then
        -- Adiciona cruz médica
        local cross1 = Instance.new("Part")
        cross1.Size = Vector3.new(0.2, 2, 0.6)
        cross1.Position = building.Position + Vector3.new(0, building.Size.Y/2 + 1, 0)
        cross1.BrickColor = BrickColor.new("Really red")
        cross1.CanCollide = false
        cross1.Anchored = true
        cross1.Parent = building
        
        local cross2 = Instance.new("Part")
        cross2.Size = Vector3.new(0.6, 2, 0.2)
        cross2.Position = building.Position + Vector3.new(0, building.Size.Y/2 + 1, 0)
        cross2.BrickColor = BrickColor.new("Really red")
        cross2.CanCollide = false
        cross2.Anchored = true
        cross2.Parent = building
    end
    
    -- Adiciona placa com nome
    local nameGui = Instance.new("SurfaceGui")
    nameGui.Face = Enum.NormalId.Front
    nameGui.Parent = building
    
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(1, 0, 0.3, 0)
    nameLabel.Position = UDim2.new(0, 0, 0, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = buildingType.displayName
    nameLabel.TextColor3 = Color3.new(1, 1, 1)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextStrokeTransparency = 0
    nameLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
    nameLabel.Parent = nameGui
    
    return building
end

-- Função para abrir menu de construção
function EnhancedBuildingManager.OpenBuildMenu(player)
    -- Verifica se o jogador tem uma base
    local playerBase = nil
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if (owner and owner.Value == player) or (partner and partner.Value == player) then
                playerBase = base
                break
            end
        end
    end
    
    if not playerBase then
        return
    end
    
    -- Coleta informações da base
    local baseSizeValue = playerBase:FindFirstChild("BaseSize")
    local buildingMaterialsValue = playerBase:FindFirstChild("BuildingMaterials")
    
    local baseInfo = {
        baseSize = baseSizeValue and baseSizeValue.Value or 100,
        buildingMaterials = buildingMaterialsValue and buildingMaterialsValue.Value or 0,
        buildingTypes = buildingTypes
    }
    
    -- Envia informações para o cliente
    updateBaseInfo:FireClient(player, baseInfo)
end

-- Função para construir estrutura
function EnhancedBuildingManager.BuildStructure(player, buildingTypeName, position)
    -- Encontra o tipo de construção
    local buildingType = nil
    for _, bType in ipairs(buildingTypes) do
        if bType.name == buildingTypeName then
            buildingType = bType
            break
        end
    end
    
    if not buildingType then
        return false
    end
    
    -- Verifica se pode construir
    local canBuild, baseOrError = canPlayerBuild(player, buildingType, position)
    if not canBuild then
        print("❌ " .. player.Name .. " não pode construir: " .. baseOrError)
        return false
    end
    
    local playerBase = baseOrError
    
    -- Deduz materiais
    local buildingMaterials = playerBase:FindFirstChild("BuildingMaterials")
    buildingMaterials.Value = buildingMaterials.Value - buildingType.cost
    
    -- Cria a construção
    local building = createBuilding(buildingType, position, playerBase)
    
    print("🏗️ " .. player.Name .. " construiu " .. buildingType.displayName .. " por " .. buildingType.cost .. " materiais!")
    return true
end

-- Conecta eventos
openBuildMenu.OnServerEvent:Connect(function(player)
    EnhancedBuildingManager.OpenBuildMenu(player)
end)

buildStructure.OnServerEvent:Connect(function(player, buildingTypeName, position)
    EnhancedBuildingManager.BuildStructure(player, buildingTypeName, position)
end)

moveStructure.OnServerEvent:Connect(function(player, building, newPosition)
    if building and building.Parent and building:FindFirstChild("BaseOwner") then
        local base = building.BaseOwner.Value
        local owner = base:FindFirstChild("Owner")
        local partner = base:FindFirstChild("Partner")
        
        -- Verifica se o jogador pode mover esta construção
        if (owner and owner.Value == player) or (partner and partner.Value == player) then
            building.Position = newPosition
            print("🔧 " .. player.Name .. " moveu " .. building.Name)
        end
    end
end)

deleteStructure.OnServerEvent:Connect(function(player, building)
    if building and building.Parent and building:FindFirstChild("BaseOwner") then
        local base = building.BaseOwner.Value
        local owner = base:FindFirstChild("Owner")
        local partner = base:FindFirstChild("Partner")
        
        -- Verifica se o jogador pode deletar esta construção
        if (owner and owner.Value == player) or (partner and partner.Value == player) then
            -- Retorna metade dos materiais
            local buildingTypeValue = building:FindFirstChild("BuildingType")
            if buildingTypeValue then
                for _, bType in ipairs(buildingTypes) do
                    if bType.name == buildingTypeValue.Value then
                        local buildingMaterials = base:FindFirstChild("BuildingMaterials")
                        if buildingMaterials then
                            buildingMaterials.Value = buildingMaterials.Value + math.floor(bType.cost / 2)
                        end
                        break
                    end
                end
            end
            
            building:Destroy()
            print("🗑️ " .. player.Name .. " removeu " .. building.Name)
        end
    end
end)

print("EnhancedBuildingManager inicializado com sucesso!")

return EnhancedBuildingManager