-- SimpleCombatScript.client.lua
-- Script simples para CombatGun que funciona diretamente

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Configurações da arma
local DAMAGE = 10 -- Dan<PERSON> de 10 por tiro
local RANGE = 90 -- Dobro da CollectorGun (45 * 2 = 90)
local FIRE_RATE = 0.2  -- Reduzido para permitir disparo mais rápido
local PROJECTILE_SPEED = 150 -- Aumentado para lasers mais rápidos

-- Controle de disparo
local lastFireTime = 0
local activeProjectiles = {} -- Lista para rastrear projéteis ativos

-- Função para criar laser instantâneo (sem física complexa)
local function createLaserEffect(startPos, endPos, hitTarget)
    -- Cria laser visual instantâneo
    local laser = Instance.new("Part")
    laser.Name = "CombatLaser"
    laser.Size = Vector3.new(0.2, 0.2, (endPos - startPos).Magnitude)
    laser.BrickColor = BrickColor.new("Bright red")
    laser.Material = Enum.Material.Neon
    laser.CanCollide = false
    laser.Anchored = true
    laser.Parent = workspace
    
    -- Posiciona o laser em linha reta
    laser.CFrame = CFrame.lookAt(startPos, endPos) * CFrame.new(0, 0, -laser.Size.Z / 2)
    
    -- Efeito de luz
    local light = Instance.new("PointLight")
    light.Color = Color3.new(1, 0, 0)
    light.Brightness = 3
    light.Range = 10
    light.Parent = laser
    
    -- Som do disparo
    local sound = Instance.new("Sound")
    sound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    sound.Volume = 0.5
    sound.Pitch = 1.8
    sound.Parent = laser
    sound:Play()
    
    -- Efeito de impacto se atingiu algo
    if hitTarget then
        local impact = Instance.new("Explosion")
        impact.Position = endPos
        impact.BlastRadius = 3
        impact.BlastPressure = 0
        impact.Visible = false
        impact.Parent = workspace
        
        -- Efeito visual de impacto
        local impactEffect = Instance.new("Part")
        impactEffect.Name = "ImpactEffect"
        impactEffect.Size = Vector3.new(2, 2, 2)
        impactEffect.Position = endPos
        impactEffect.BrickColor = BrickColor.new("Bright red")
        impactEffect.Material = Enum.Material.Neon
        impactEffect.CanCollide = false
        impactEffect.Anchored = true
        impactEffect.Shape = Enum.PartType.Ball
        impactEffect.Parent = workspace
        
        -- Efeito de fade do impacto
        spawn(function()
            for i = 1, 10 do
                impactEffect.Transparency = i / 10
                impactEffect.Size = impactEffect.Size * 1.1
                wait(0.05)
            end
            impactEffect:Destroy()
        end)
    end
    
    -- Remove o laser após 0.3 segundos
    spawn(function()
        wait(0.3)
        if laser and laser.Parent then
            laser:Destroy()
        end
    end)
    
    return laser
end

-- Função para causar dano via RemoteEvent
local function dealDamage(hitPlayer, damage)
    if hitPlayer and hitPlayer ~= player then
        -- Envia evento para o servidor causar dano
        local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
        if remoteEvents then
            local dealDamageEvent = remoteEvents:FindFirstChild("DealDamage")
            if dealDamageEvent then
                dealDamageEvent:FireServer(hitPlayer, damage)
                print("💥 " .. player.Name .. " atingiu " .. hitPlayer.Name .. " causando " .. damage .. " de dano!")
            end
        end
    end
end

-- Função para disparar
local function fire()
    local currentTime = tick()
    if currentTime - lastFireTime < FIRE_RATE then
        return
    end
    
    lastFireTime = currentTime
    
    -- Raycast para detectar alvo
    local character = player.Character
    if not character then return end
    
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    -- Posição de origem (da ferramenta)
    local startPos = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 2
    local direction = (mouse.Hit.Position - startPos).Unit
    local endPos = startPos + direction * RANGE
    
    -- Raycast
    local raycastParams = RaycastParams.new()
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
    raycastParams.FilterDescendantsInstances = {character}
    
    local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
    local hitTarget = false
    
    if raycastResult then
        endPos = raycastResult.Position
        hitTarget = true
        
        -- Verifica se atingiu um jogador ou NPC
        local hitCharacter = raycastResult.Instance.Parent
        local hitHumanoid = hitCharacter:FindFirstChild("Humanoid")
        local hitPlayer = Players:GetPlayerFromCharacter(hitCharacter)
        
        if hitPlayer and hitPlayer ~= player then
            -- Causa dano em jogador via servidor
            dealDamage(hitPlayer, DAMAGE)
            print("🎯 Atingiu jogador: " .. hitPlayer.Name)
        elseif hitHumanoid and not hitPlayer then
            -- Causa dano em NPC via servidor
            dealDamage(hitCharacter, DAMAGE)
            print("🎯 Atingiu NPC: " .. hitCharacter.Name)
        end
    end
    
    -- Cria efeito visual (laser instantâneo)
    createLaserEffect(startPos, endPos, hitTarget)
    print("🔫 " .. player.Name .. " disparou CombatGun!")
end

-- Monitora quando o jogador equipa a CombatGun
local function monitorCombatGun()
    local character = player.Character or player.CharacterAdded:Wait()
    
    character.ChildAdded:Connect(function(child)
        if child.Name == "CombatGun" and child:IsA("Tool") then
            print("🔫 CombatGun equipada!")
            mouse.Icon = "rbxasset://textures/GunCursor.png"
            
            child.Activated:Connect(fire)
            
            child.Unequipped:Connect(function()
                mouse.Icon = ""
                print("🔫 CombatGun desequipada!")
            end)
        end
    end)
    
    -- Verifica se já tem a ferramenta equipada
    local existingTool = character:FindFirstChild("CombatGun")
    if existingTool and existingTool:IsA("Tool") then
        print("🔫 CombatGun já equipada!")
        mouse.Icon = "rbxasset://textures/GunCursor.png"
        
        existingTool.Activated:Connect(fire)
        
        existingTool.Unequipped:Connect(function()
            mouse.Icon = ""
            print("🔫 CombatGun desequipada!")
        end)
    end
end

-- Inicializa o monitoramento
if player.Character then
    monitorCombatGun()
end

player.CharacterAdded:Connect(function()
    wait(1)
    monitorCombatGun()
end)

print("✅ SimpleCombatScript carregado!")