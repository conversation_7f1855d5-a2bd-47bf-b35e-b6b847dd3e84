-- SimpleCombatScript.client.lua
-- Script simples para CombatGun que funciona diretamente

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Configurações da arma
local DAMAGE = 10 -- Dan<PERSON> de 10 por tiro
local RANGE = 90 -- Dobro da CollectorGun (45 * 2 = 90)
local FIRE_RATE = 0.2  -- Reduzido para permitir disparo mais rápido
local PROJECTILE_SPEED = 150 -- Aumentado para lasers mais rápidos

-- Controle de disparo
local lastFireTime = 0
local activeProjectiles = {} -- Lista para rastrear projéteis ativos

-- Função para criar laser instantâneo (sem física complexa)
local function createLaserEffect(startPos, endPos, hitTarget)
    -- Cria laser visual instantâneo
    local laser = Instance.new("Part")
    laser.Name = "CombatLaser"
    laser.Size = Vector3.new(0.2, 0.2, (endPos - startPos).Magnitude)
    laser.BrickColor = BrickColor.new("Bright red")
    laser.Material = Enum.Material.Neon
    laser.CanCollide = false
    laser.Anchored = true
    laser.Parent = workspace
    
    -- Posiciona o laser em linha reta
    laser.CFrame = CFrame.lookAt(startPos, endPos) * CFrame.new(0, 0, -laser.Size.Z / 2)
    
    -- Efeito de luz
    local light = Instance.new("PointLight")
    light.Color = Color3.new(1, 0, 0)
    light.Brightness = 3
    light.Range = 10
    light.Parent = laser
    
    -- Som do disparo
    local sound = Instance.new("Sound")
    sound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    sound.Volume = 0.5
    sound.Pitch = 1.8
    sound.Parent = laser
    sound:Play()
    
    -- Efeito de impacto se atingiu algo
    if hitTarget then
        local impact = Instance.new("Explosion")
        impact.Position = endPos
        impact.BlastRadius = 3
        impact.BlastPressure = 0
        impact.Visible = false
        impact.Parent = workspace
        
        -- Efeito visual de impacto
        local impactEffect = Instance.new("Part")
        impactEffect.Name = "ImpactEffect"
        impactEffect.Size = Vector3.new(2, 2, 2)
        impactEffect.Position = endPos
        impactEffect.BrickColor = BrickColor.new("Bright red")
        impactEffect.Material = Enum.Material.Neon
        impactEffect.CanCollide = false
        impactEffect.Anchored = true
        impactEffect.Shape = Enum.PartType.Ball
        impactEffect.Parent = workspace
        
        -- Efeito de fade do impacto
        spawn(function()
            for i = 1, 10 do
                impactEffect.Transparency = i / 10
                impactEffect.Size = impactEffect.Size * 1.1
                wait(0.05)
            end
            impactEffect:Destroy()
        end)
    end
    
    -- Sincroniza com outros jogadores
    local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
    if remoteEvents then
        local showLaserEffect = remoteEvents:FindFirstChild("ShowLaserEffect")
        if showLaserEffect then
            showLaserEffect:FireServer(startPos, endPos, hitTarget)
        end
    end

    -- Remove o laser após 0.3 segundos
    spawn(function()
        wait(0.3)
        if laser and laser.Parent then
            laser:Destroy()
        end
    end)

    return laser
end

-- Função para causar dano via RemoteEvent
local function dealDamage(target, damage)
    local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
    if not remoteEvents then
        print("❌ RemoteEvents não encontrado!")
        return
    end

    local dealDamageEvent = remoteEvents:FindFirstChild("DealDamage")
    if not dealDamageEvent then
        print("❌ DealDamage event não encontrado!")
        return
    end

    -- Se target é um jogador
    if target and target:IsA("Player") and target ~= player then
        dealDamageEvent:FireServer(target, damage)
        print("💥 " .. player.Name .. " atingiu jogador " .. target.Name .. " causando " .. damage .. " de dano!")
    -- Se target é um NPC (Model com Humanoid)
    elseif target and target:IsA("Model") and target:FindFirstChild("Humanoid") then
        dealDamageEvent:FireServer(target, damage)
        print("💥 " .. player.Name .. " atingiu NPC " .. target.Name .. " causando " .. damage .. " de dano!")
    else
        print("❌ Alvo inválido para dano: " .. tostring(target))
    end
end

-- Função para disparar
local function fire()
    local currentTime = tick()
    if currentTime - lastFireTime < FIRE_RATE then
        return
    end
    
    lastFireTime = currentTime
    
    -- Raycast para detectar alvo
    local character = player.Character
    if not character then return end
    
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    -- Posição de origem (da ferramenta)
    local startPos = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 2
    local direction = (mouse.Hit.Position - startPos).Unit
    local endPos = startPos + direction * RANGE
    
    -- Raycast
    local raycastParams = RaycastParams.new()
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
    raycastParams.FilterDescendantsInstances = {character}
    
    local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
    local hitTarget = false
    
    if raycastResult then
        endPos = raycastResult.Position
        hitTarget = true

        -- Verifica se atingiu um jogador ou NPC
        local hitPart = raycastResult.Instance
        local hitCharacter = hitPart.Parent

        -- Tenta encontrar o character subindo na hierarquia
        if not hitCharacter:FindFirstChild("Humanoid") then
            hitCharacter = hitPart.Parent.Parent
        end

        local hitHumanoid = hitCharacter:FindFirstChild("Humanoid")
        local hitPlayer = Players:GetPlayerFromCharacter(hitCharacter)

        if hitHumanoid then
            if hitPlayer and hitPlayer ~= player then
                -- Causa dano em jogador via servidor
                dealDamage(hitPlayer, DAMAGE)
                print("🎯 Atingiu jogador: " .. hitPlayer.Name .. " na parte: " .. hitPart.Name)
            elseif not hitPlayer then
                -- Causa dano em NPC via servidor
                dealDamage(hitCharacter, DAMAGE)
                print("🎯 Atingiu NPC: " .. hitCharacter.Name .. " na parte: " .. hitPart.Name)
            else
                print("🎯 Atingiu a si mesmo - ignorado")
            end
        else
            print("🎯 Atingiu objeto sem Humanoid: " .. hitPart.Name .. " em " .. hitCharacter.Name)
        end
    end
    
    -- Cria efeito visual (laser instantâneo)
    createLaserEffect(startPos, endPos, hitTarget)
    print("🔫 " .. player.Name .. " disparou CombatGun!")
end

-- Monitora quando o jogador equipa a CombatGun
local function monitorCombatGun()
    local character = player.Character or player.CharacterAdded:Wait()
    
    character.ChildAdded:Connect(function(child)
        if child.Name == "CombatGun" and child:IsA("Tool") then
            print("🔫 CombatGun equipada!")
            mouse.Icon = "rbxasset://textures/GunCursor.png"
            
            child.Activated:Connect(fire)
            
            child.Unequipped:Connect(function()
                mouse.Icon = ""
                print("🔫 CombatGun desequipada!")
            end)
        end
    end)
    
    -- Verifica se já tem a ferramenta equipada
    local existingTool = character:FindFirstChild("CombatGun")
    if existingTool and existingTool:IsA("Tool") then
        print("🔫 CombatGun já equipada!")
        mouse.Icon = "rbxasset://textures/GunCursor.png"
        
        existingTool.Activated:Connect(fire)
        
        existingTool.Unequipped:Connect(function()
            mouse.Icon = ""
            print("🔫 CombatGun desequipada!")
        end)
    end
end

-- Inicializa o monitoramento
if player.Character then
    monitorCombatGun()
end

player.CharacterAdded:Connect(function()
    wait(1)
    monitorCombatGun()
end)

-- Sistema para receber lasers de outros jogadores
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local showLaserEffect = remoteEvents:WaitForChild("ShowLaserEffect")

showLaserEffect.OnClientEvent:Connect(function(shooterName, startPos, endPos, hitTarget)
    -- Cria laser visual para outros jogadores
    local otherLaser = Instance.new("Part")
    otherLaser.Name = "OtherPlayerLaser_" .. shooterName
    otherLaser.Size = Vector3.new(0.15, 0.15, (endPos - startPos).Magnitude)
    otherLaser.BrickColor = BrickColor.new("Bright red")
    otherLaser.Material = Enum.Material.Neon
    otherLaser.CanCollide = false
    otherLaser.Anchored = true
    otherLaser.Parent = workspace

    -- Posiciona o laser
    otherLaser.CFrame = CFrame.lookAt(startPos, endPos) * CFrame.new(0, 0, -otherLaser.Size.Z / 2)

    -- Efeito de luz mais sutil
    local light = Instance.new("PointLight")
    light.Color = Color3.new(1, 0.5, 0.5)
    light.Brightness = 2
    light.Range = 8
    light.Parent = otherLaser

    -- Som mais baixo para outros jogadores
    local sound = Instance.new("Sound")
    sound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    sound.Volume = 0.3
    sound.Pitch = 1.8
    sound.Parent = otherLaser
    sound:Play()

    -- Remove após tempo menor
    spawn(function()
        wait(0.2)
        if otherLaser and otherLaser.Parent then
            otherLaser:Destroy()
        end
    end)

    print("👁️ Vendo laser de " .. shooterName)
end)

print("✅ SimpleCombatScript carregado!")