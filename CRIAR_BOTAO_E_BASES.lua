-- CRIAR_BOTAO_E_BASES.lua
-- Execute este script UMA VEZ no Command Bar do Roblox Studio para criar botões E nas bases

-- Função para criar botão de interação em uma base
local function createInteractionButton(base)
    if not base or not base:Find<PERSON>irstChild("SpawnLocation") then return end
    
    local spawnLocation = base:FindFirstChild("SpawnLocation")
    
    -- Remove botão existente se houver
    local existingButton = base:FindFirstChild("InteractionButton")
    if existingButton then existingButton:Destroy() end
    
    -- Cria botão de interação
    local interactionButton = Instance.new("Part")
    interactionButton.Name = "InteractionButton"
    interactionButton.Size = Vector3.new(2, 0.5, 2)
    interactionButton.Position = spawnLocation.Position + Vector3.new(0, 1, 0) -- Acima do SpawnLocation
    interactionButton.BrickColor = BrickColor.new("Bright yellow")
    interactionButton.Material = Enum.Material.Neon
    interactionButton.Anchored = true
    interactionButton.CanCollide = false
    interactionButton.Shape = Enum.PartType.Cylinder
    interactionButton.Parent = base
    
    -- Adiciona efeito de rotação
    local rotateConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if interactionButton and interactionButton.Parent then
            interactionButton.CFrame = interactionButton.CFrame * CFrame.Angles(0, 0.05, 0)
        else
            rotateConnection:Disconnect()
        end
    end)
    
    -- Adiciona luz
    local light = Instance.new("PointLight")
    light.Color = Color3.new(1, 1, 0)
    light.Brightness = 2
    light.Range = 10
    light.Parent = interactionButton
    
    -- Adiciona texto flutuante
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Size = UDim2.new(0, 200, 0, 100)
    billboardGui.StudsOffset = Vector3.new(0, 2, 0)
    billboardGui.Parent = interactionButton
    
    -- Frame do texto
    local textFrame = Instance.new("Frame")
    textFrame.Size = UDim2.new(1, 0, 1, 0)
    textFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    textFrame.BackgroundTransparency = 0.3
    textFrame.BorderSizePixel = 2
    textFrame.BorderColor3 = Color3.new(1, 1, 0)
    textFrame.Parent = billboardGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = textFrame
    
    -- Texto principal
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 0.6, 0)
    textLabel.Position = UDim2.new(0, 0, 0, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "🔨 PRESSIONE [E]"
    textLabel.TextColor3 = Color3.new(1, 1, 1)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = textFrame
    
    -- Texto secundário
    local subText = Instance.new("TextLabel")
    subText.Size = UDim2.new(1, 0, 0.4, 0)
    subText.Position = UDim2.new(0, 0, 0.6, 0)
    subText.BackgroundTransparency = 1
    subText.Text = "Construções & Melhorias"
    subText.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    subText.TextScaled = true
    subText.Font = Enum.Font.Gotham
    subText.Parent = textFrame
    
    -- Efeito de pulsação
    local pulseConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if textFrame and textFrame.Parent then
            local time = tick()
            local alpha = (math.sin(time * 4) + 1) / 2 -- Oscila entre 0 e 1
            textFrame.BackgroundTransparency = 0.3 + (alpha * 0.4)
        else
            pulseConnection:Disconnect()
        end
    end)
    
    print("✅ Botão de interação criado para " .. base.Name)
end

-- Cria botões para todas as bases
local basesFound = 0
for i = 1, 8 do
    local baseName = "Base_" .. i
    local base = workspace:FindFirstChild(baseName)
    if base then
        createInteractionButton(base)
        basesFound = basesFound + 1
    else
        print("⚠️ " .. baseName .. " não encontrada no workspace")
    end
end

print("🎉 BOTÕES DE INTERAÇÃO CRIADOS!")
print("📊 " .. basesFound .. " botões criados")
print("🎯 Agora os jogadores podem pressionar E para abrir o menu de construções!")
print("💡 Os botões estão localizados acima do SpawnLocation de cada base")
print("🔄 Execute o jogo para testar a funcionalidade!")