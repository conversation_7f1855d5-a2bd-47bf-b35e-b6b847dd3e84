-- BaseBoundaryManager.lua
-- Gerencia as linhas quadradas ao redor das bases para mostrar o território de construção

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Carrega configurações
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))

local BaseBoundaryManager = {}

-- Função para criar linha quadrada ao redor da base
local function createBaseBoundary(base)
    if not base then return end
    
    local barrier = base:FindFirstChild("Barrier")
    local teamColorValue = base:FindFirstChild("TeamColor")
    
    if not barrier or not teamColorValue then return end
    
    -- Remove boundary existente se houver
    local existingBoundary = base:FindFirstChild("BaseBoundary")
    if existingBoundary then
        existingBoundary:Destroy()
    end
    
    -- Cria folder para organizar as linhas
    local boundaryFolder = Instance.new("Folder")
    boundaryFolder.Name = "BaseBoundary"
    boundaryFolder.Parent = base
    
    -- Configurações da linha
    local barrierRadius = barrier.Size.X / 2
    local lineThickness = 1 -- Aumentado para ser mais visível
    local lineHeight = 0.5 -- Reduzido para ficar mais no chão
    local lineColor = BrickColor.new(teamColorValue.Value)
    local barrierCenter = barrier.Position
    
    -- Calcula os pontos dos cantos do quadrado (na altura da BasePlatform)
    local basePlatform = base:FindFirstChild("BasePlatform")
    local groundY = 5 -- Altura padrão
    if basePlatform then
        groundY = basePlatform.Position.Y + basePlatform.Size.Y/2 + 0.5 -- Ligeiramente acima da plataforma
    end
    local corners = {
        Vector3.new(barrierCenter.X - barrierRadius, groundY, barrierCenter.Z - barrierRadius),
        Vector3.new(barrierCenter.X + barrierRadius, groundY, barrierCenter.Z - barrierRadius),
        Vector3.new(barrierCenter.X + barrierRadius, groundY, barrierCenter.Z + barrierRadius),
        Vector3.new(barrierCenter.X - barrierRadius, groundY, barrierCenter.Z + barrierRadius)
    }
    
    -- Cria as 4 linhas do quadrado
    for i = 1, 4 do
        local startPoint = corners[i]
        local endPoint = corners[i % 4 + 1] -- Próximo ponto (volta para 1 quando i=4)
        
        -- Calcula direção e distância
        local direction = (endPoint - startPoint)
        local distance = direction.Magnitude
        local midPoint = startPoint + direction / 2
        
        -- Cria a linha
        local line = Instance.new("Part")
        line.Name = "BoundaryLine" .. i
        line.Size = Vector3.new(lineThickness, lineHeight, distance)
        line.Position = midPoint
        line.BrickColor = lineColor
        line.Material = Enum.Material.Neon
        line.Anchored = true
        line.CanCollide = false
        line.Transparency = 0.1 -- Menos transparente para ser mais visível
        line.Parent = boundaryFolder
        
        -- Orienta a linha na direção correta
        line.CFrame = CFrame.lookAt(midPoint, endPoint)
        
        -- Adiciona efeito de brilho mais forte
        local pointLight = Instance.new("PointLight")
        pointLight.Color = lineColor.Color
        pointLight.Brightness = 1.5 -- Mais brilhante
        pointLight.Range = 8 -- Maior alcance
        pointLight.Parent = line
        
        -- Adiciona efeito de seleção para destacar
        local selectionBox = Instance.new("SelectionBox")
        selectionBox.Adornee = line
        selectionBox.Color3 = lineColor.Color
        selectionBox.LineThickness = 0.1
        selectionBox.Transparency = 0.5
        selectionBox.Parent = line
    end
    
    print("Boundary criado para " .. base.Name .. " com cor " .. teamColorValue.Value)
end

-- Função para atualizar boundary quando a base muda de tamanho
local function updateBaseBoundary(base)
    createBaseBoundary(base) -- Recria o boundary com o novo tamanho
end

-- Função para remover boundary quando base é liberada
local function removeBaseBoundary(base)
    local existingBoundary = base:FindFirstChild("BaseBoundary")
    if existingBoundary then
        existingBoundary:Destroy()
    end
end

-- Função para criar boundaries para todas as bases existentes
function BaseBoundaryManager.CreateAllBoundaries()
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            if owner and owner.Value then
                -- Base tem dono, cria boundary
                createBaseBoundary(base)
            end
        end
    end
end

-- Função para criar boundary quando base é reivindicada
function BaseBoundaryManager.OnBaseClaimed(base)
    createBaseBoundary(base)
end

-- Função para remover boundary quando base é liberada
function BaseBoundaryManager.OnBaseReleased(base)
    removeBaseBoundary(base)
end

-- Função para atualizar boundary quando base muda
function BaseBoundaryManager.OnBaseUpdated(base)
    updateBaseBoundary(base)
end

-- Monitora mudanças nas bases (incluindo bases existentes)
local function monitorBaseChanges()
    -- Função para configurar monitoramento de uma base
    local function setupBaseMonitoring(base)
        if not base.Name:match("Base_") then return end
        
        local owner = base:FindFirstChild("Owner")
        if owner then
            -- Monitora mudanças no owner
            owner.Changed:Connect(function()
                wait(0.1)
                if owner.Value then
                    BaseBoundaryManager.OnBaseClaimed(base)
                else
                    BaseBoundaryManager.OnBaseReleased(base)
                end
            end)
            
            -- Se já tem owner, cria boundary
            if owner.Value then
                BaseBoundaryManager.OnBaseClaimed(base)
            end
        end
        
        -- Monitora mudanças no tamanho da base
        local baseSize = base:FindFirstChild("BaseSize")
        if baseSize then
            baseSize.Changed:Connect(function()
                wait(0.1)
                if owner and owner.Value then
                    BaseBoundaryManager.OnBaseUpdated(base)
                end
            end)
        end
    end
    
    -- Configura monitoramento para bases existentes
    for _, child in ipairs(workspace:GetChildren()) do
        if child.Name:match("Base_") then
            setupBaseMonitoring(child)
        end
    end
    
    -- Monitora quando novas bases são criadas
    workspace.ChildAdded:Connect(function(child)
        if child.Name:match("Base_") then
            wait(0.5) -- Aguarda a base ser totalmente configurada
            setupBaseMonitoring(child)
        end
    end)
end

-- Inicializa o sistema
spawn(function()
    wait(2) -- Aguarda o jogo carregar
    BaseBoundaryManager.CreateAllBoundaries()
    monitorBaseChanges()
end)

print("BaseBoundaryManager inicializado com sucesso!")

return BaseBoundaryManager