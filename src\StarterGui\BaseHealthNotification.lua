-- BaseHealthNotification.lua
-- Sistema para mostrar notificações de vida das bases sendo atacadas

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitFor<PERSON>hild("RemoteEvents")
local updateBaseHealth = remoteEvents:WaitForChild("UpdateBaseHealth")

-- Tabela para rastrear notificações ativas
local activeNotifications = {}

-- Função para criar notificação de vida da base
local function createBaseHealthNotification(baseName, healthText, healthPercentage)
    local base = workspace:FindFirstChild(baseName)
    if not base then return end

    local basePlatform = base:FindFirstChild("BasePlatform")
    if not basePlatform then return end

    -- Remove notificação existente se houver
    if activeNotifications[baseName] then
        if activeNotifications[baseName].gui and activeNotifications[baseName].gui.Parent then
            activeNotifications[baseName].gui:Destroy()
        end
    end

    -- Cria BillboardGui
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "BaseHealthNotification"
    billboardGui.Size = UDim2.new(0, 150, 0, 40)
    billboardGui.StudsOffset = Vector3.new(0, 8, 0)
    billboardGui.Parent = basePlatform

    -- Frame de fundo
    local backgroundFrame = Instance.new("Frame")
    backgroundFrame.Size = UDim2.new(1, 0, 1, 0)
    backgroundFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    backgroundFrame.BackgroundTransparency = 0.2
    backgroundFrame.BorderSizePixel = 2
    backgroundFrame.BorderColor3 = Color3.new(1, 1, 1)
    backgroundFrame.Parent = billboardGui

    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 6)
    corner.Parent = backgroundFrame

    -- Barra de vida da base
    local healthBarBG = Instance.new("Frame")
    healthBarBG.Size = UDim2.new(1, -10, 0, 15)
    healthBarBG.Position = UDim2.new(0, 5, 0, 5)
    healthBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    healthBarBG.BorderSizePixel = 1
    healthBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    healthBarBG.Parent = backgroundFrame

    local healthBar = Instance.new("Frame")
    healthBar.Name = "HealthBar"
    healthBar.Size = UDim2.new(healthPercentage, 0, 1, 0)
    healthBar.Position = UDim2.new(0, 0, 0, 0)
    healthBar.BorderSizePixel = 0
    healthBar.Parent = healthBarBG

    -- Cor da barra baseada na vida
    if healthPercentage > 0.6 then
        healthBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
    elseif healthPercentage > 0.3 then
        healthBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
    else
        healthBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
    end

    -- Texto da vida
    local healthTextLabel = Instance.new("TextLabel")
    healthTextLabel.Name = "HealthText"
    healthTextLabel.Size = UDim2.new(1, 0, 0, 20)
    healthTextLabel.Position = UDim2.new(0, 0, 0, 20)
    healthTextLabel.BackgroundTransparency = 1
    healthTextLabel.Text = "🏠 " .. baseName .. ": " .. healthText
    healthTextLabel.TextColor3 = Color3.new(1, 1, 1)
    healthTextLabel.TextScaled = true
    healthTextLabel.Font = Enum.Font.SourceSansBold
    healthTextLabel.TextStrokeTransparency = 0
    healthTextLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
    healthTextLabel.Parent = backgroundFrame

    -- Salva referência
    activeNotifications[baseName] = {
        gui = billboardGui,
        healthBar = healthBar,
        healthText = healthTextLabel,
        lastUpdate = tick()
    }

    -- Remove automaticamente após 5 segundos sem atualização
    spawn(function()
        wait(5)
        if activeNotifications[baseName] and 
           activeNotifications[baseName].gui == billboardGui and
           (tick() - activeNotifications[baseName].lastUpdate) > 4 then
            if billboardGui and billboardGui.Parent then
                billboardGui:Destroy()
            end
            activeNotifications[baseName] = nil
        end
    end)
end

-- Função para atualizar notificação existente
local function updateBaseHealthNotification(baseName, healthText, healthPercentage)
    local notification = activeNotifications[baseName]
    if not notification or not notification.gui or not notification.gui.Parent then
        -- Cria nova se não existir
        createBaseHealthNotification(baseName, healthText, healthPercentage)
        return
    end

    -- Atualiza barra de vida
    notification.healthBar.Size = UDim2.new(healthPercentage, 0, 1, 0)

    -- Atualiza cor
    if healthPercentage > 0.6 then
        notification.healthBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
    elseif healthPercentage > 0.3 then
        notification.healthBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
    else
        notification.healthBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
    end

    -- Atualiza texto
    notification.healthText.Text = "🏠 " .. baseName .. ": " .. healthText

    -- Atualiza timestamp
    notification.lastUpdate = tick()
end

-- Conecta evento de atualização de vida da base
updateBaseHealth.OnClientEvent:Connect(function(baseName, healthText, healthPercentage)
    updateBaseHealthNotification(baseName, healthText, healthPercentage)
end)

-- Limpa notificações quando bases são removidas
workspace.ChildRemoved:Connect(function(child)
    if child.Name:match("Base_") and activeNotifications[child.Name] then
        if activeNotifications[child.Name].gui and activeNotifications[child.Name].gui.Parent then
            activeNotifications[child.Name].gui:Destroy()
        end
        activeNotifications[child.Name] = nil
    end
end)

print("BaseHealthNotification carregado com sucesso!")