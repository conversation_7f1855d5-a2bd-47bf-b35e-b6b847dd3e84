{"name": "project", "tree": {"$className": "DataModel", "ReplicatedStorage": {"$className": "ReplicatedStorage", "$ignoreUnknownInstances": true, "$path": "src/ReplicatedStorage"}, "ServerScriptService": {"$className": "ServerScriptService", "$ignoreUnknownInstances": true, "$path": "src/ServerScriptService"}, "ServerStorage": {"$className": "ServerStorage", "$ignoreUnknownInstances": true, "$path": "src/ServerStorage"}, "StarterGui": {"$className": "<PERSON><PERSON><PERSON><PERSON>", "$ignoreUnknownInstances": true, "$path": "src/StarterGui"}, "StarterPack": {"$className": "Starter<PERSON><PERSON>", "$ignoreUnknownInstances": true, "$path": "src/StarterPack"}, "TestService": {"$className": "TestService", "$ignoreUnknownInstances": true, "$path": "src/TestService"}}}