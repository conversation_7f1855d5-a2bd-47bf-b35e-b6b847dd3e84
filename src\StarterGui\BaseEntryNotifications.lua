-- BaseEntryNotifications.lua
-- Sistema de notificações para entrada em bases e efeitos de cura

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local playerEnteredEnemyBase = remoteEvents:WaitForChild("PlayerEnteredEnemyBase")
local playerLeftEnemyBase = remoteEvents:WaitForChild("PlayerLeftEnemyBase")
local showHealingEffect = remoteEvents:WaitFor<PERSON>hild("ShowHealingEffect")

-- Variáveis para notificações
local currentNotification = nil
local healingEffectActive = false

-- Função para criar notificação de entrada em base inimiga
local function showEnemyBaseNotification(baseName)
    -- Remove notificação existente
    if currentNotification then
        currentNotification:Destroy()
    end

    local playerGui = player:WaitF<PERSON><PERSON>hild("PlayerGui")

    -- Cria ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "BaseEntryNotification"
    screenGui.Parent = playerGui

    -- Frame da notificação
    local notificationFrame = Instance.new("Frame")
    notificationFrame.Size = UDim2.new(0, 400, 0, 80)
    notificationFrame.Position = UDim2.new(0.5, -200, 0, -100) -- Começa fora da tela
    notificationFrame.BackgroundColor3 = Color3.new(1, 0, 0)
    notificationFrame.BackgroundTransparency = 0.1
    notificationFrame.BorderSizePixel = 3
    notificationFrame.BorderColor3 = Color3.new(1, 1, 1)
    notificationFrame.Parent = screenGui

    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = notificationFrame

    -- Texto da notificação
    local notificationText = Instance.new("TextLabel")
    notificationText.Size = UDim2.new(1, 0, 0.6, 0)
    notificationText.Position = UDim2.new(0, 0, 0, 0)
    notificationText.BackgroundTransparency = 1
    notificationText.Text = "⚠️ INVASÃO DETECTADA!"
    notificationText.TextColor3 = Color3.new(1, 1, 1)
    notificationText.TextScaled = true
    notificationText.Font = Enum.Font.SourceSansBold
    notificationText.TextStrokeTransparency = 0
    notificationText.TextStrokeColor3 = Color3.new(0, 0, 0)
    notificationText.Parent = notificationFrame

    -- Texto da base
    local baseText = Instance.new("TextLabel")
    baseText.Size = UDim2.new(1, 0, 0.4, 0)
    baseText.Position = UDim2.new(0, 0, 0.6, 0)
    baseText.BackgroundTransparency = 1
    baseText.Text = "Você está em " .. baseName .. " - Tomando Dano!"
    baseText.TextColor3 = Color3.new(1, 1, 1)
    baseText.TextScaled = true
    baseText.Font = Enum.Font.SourceSans
    baseText.TextStrokeTransparency = 0
    baseText.TextStrokeColor3 = Color3.new(0, 0, 0)
    baseText.Parent = notificationFrame

    -- Animação de entrada
    local enterTween = TweenService:Create(
        notificationFrame,
        TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
        {Position = UDim2.new(0.5, -200, 0, 20)}
    )
    enterTween:Play()

    -- Efeito de pulsação
    spawn(function()
        while notificationFrame and notificationFrame.Parent do
            local pulseTween = TweenService:Create(
                notificationFrame,
                TweenInfo.new(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
                {BackgroundTransparency = 0.3}
            )
            pulseTween:Play()
            pulseTween.Completed:Wait()
            
            if notificationFrame and notificationFrame.Parent then
                local pulseTween2 = TweenService:Create(
                    notificationFrame,
                    TweenInfo.new(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
                    {BackgroundTransparency = 0.1}
                )
                pulseTween2:Play()
                pulseTween2.Completed:Wait()
            end
        end
    end)

    currentNotification = screenGui
end

-- Função para remover notificação de base inimiga
local function hideEnemyBaseNotification()
    if currentNotification then
        local notificationFrame = currentNotification:FindFirstChild("Frame")
        if notificationFrame then
            -- Animação de saída
            local exitTween = TweenService:Create(
                notificationFrame,
                TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
                {Position = UDim2.new(0.5, -200, 0, -100)}
            )
            exitTween:Play()
            exitTween.Completed:Connect(function()
                if currentNotification then
                    currentNotification:Destroy()
                    currentNotification = nil
                end
            end)
        else
            currentNotification:Destroy()
            currentNotification = nil
        end
    end
end

-- Função para mostrar efeito de cura
local function showPlayerHealingEffect()
    if healingEffectActive then return end
    
    healingEffectActive = true
    
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        healingEffectActive = false
        return
    end

    local humanoidRootPart = player.Character.HumanoidRootPart

    -- Cria efeito de partículas de cura
    for i = 1, 8 do
        local healParticle = Instance.new("Part")
        healParticle.Name = "HealParticle"
        healParticle.Size = Vector3.new(0.5, 0.5, 0.5)
        healParticle.Position = humanoidRootPart.Position + Vector3.new(
            math.random(-3, 3),
            math.random(-1, 1),
            math.random(-3, 3)
        )
        healParticle.BrickColor = BrickColor.new("Lime green")
        healParticle.Material = Enum.Material.Neon
        healParticle.CanCollide = false
        healParticle.Anchored = false
        healParticle.Shape = Enum.PartType.Ball
        healParticle.Parent = workspace

        -- Aplica movimento em direção ao jogador
        local bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(2000, 2000, 2000)
        bodyVelocity.Velocity = (humanoidRootPart.Position - healParticle.Position).Unit * 10 + Vector3.new(0, 5, 0)
        bodyVelocity.Parent = healParticle

        -- Remove partícula após 1 segundo
        game:GetService("Debris"):AddItem(healParticle, 1)
    end

    -- Efeito de luz verde no jogador
    local healLight = Instance.new("PointLight")
    healLight.Color = Color3.new(0, 1, 0)
    healLight.Brightness = 3
    healLight.Range = 8
    healLight.Parent = humanoidRootPart

    -- Remove luz após 1 segundo
    spawn(function()
        wait(1)
        if healLight and healLight.Parent then
            healLight:Destroy()
        end
        healingEffectActive = false
    end)

    -- Som de cura
    local healSound = Instance.new("Sound")
    healSound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    healSound.Volume = 0.3
    healSound.Pitch = 1.5
    healSound.Parent = humanoidRootPart
    healSound:Play()

    healSound.Ended:Connect(function()
        healSound:Destroy()
    end)
end

-- Conecta eventos
playerEnteredEnemyBase.OnClientEvent:Connect(function(baseName)
    showEnemyBaseNotification(baseName)
end)

playerLeftEnemyBase.OnClientEvent:Connect(function()
    hideEnemyBaseNotification()
end)

showHealingEffect.OnClientEvent:Connect(function()
    showPlayerHealingEffect()
end)

print("BaseEntryNotifications carregado com sucesso!")