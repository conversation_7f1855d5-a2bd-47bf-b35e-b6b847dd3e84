-- DropManager.lua
-- Gerencia itens dropados no servidor para que todos vejam

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Debris = game:GetService("Debris")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local dropItemEvent = remoteEvents:WaitForChild("DropItem")

-- Função para criar item dropado no servidor
local function createDroppedItem(player, position)
    if not player.Character then
        print("❌ Personagem do jogador não encontrado para drop!")
        return
    end

    -- Ajusta posição para garantir que não spawne dentro do chão
    local adjustedPosition = Vector3.new(position.X, math.max(position.Y, 5), position.Z)

    -- Cria item no chão
    local droppedItem = Instance.new("Part")
    droppedItem.Name = "DroppedResource"
    droppedItem.Size = Vector3.new(3, 3, 3) -- Aumentado para ser mais visível
    droppedItem.BrickColor = BrickColor.new("Bright yellow")
    droppedItem.Material = Enum.Material.ForceField -- <PERSON><PERSON> visível
    droppedItem.CanCollide = false -- Permite que jogadores passem através
    droppedItem.Anchored = true -- Evita que role
    droppedItem.Shape = Enum.PartType.Ball
    droppedItem.Parent = workspace
    droppedItem.Position = adjustedPosition
    droppedItem.TopSurface = Enum.SurfaceType.Smooth
    droppedItem.BottomSurface = Enum.SurfaceType.Smooth
    
    -- Adiciona efeito de brilho
    local light = Instance.new("PointLight")
    light.Color = Color3.new(1, 1, 0)
    light.Brightness = 2
    light.Range = 10
    light.Parent = droppedItem
    
    -- Adiciona texto flutuante
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Size = UDim2.new(0, 100, 0, 50)
    billboardGui.StudsOffset = Vector3.new(0, 3, 0)
    billboardGui.Parent = droppedItem
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "💎 RECURSO"
    textLabel.TextColor3 = Color3.new(1, 1, 1)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = billboardGui
    
    -- Sistema de coleta por proximidade (mais confiável que toque)
    local isBeingCollected = false
    local collectConnection

    collectConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if isBeingCollected then return end

        -- Verifica todos os jogadores próximos
        for _, checkPlayer in pairs(Players:GetPlayers()) do
            if checkPlayer.Character and checkPlayer.Character:FindFirstChild("HumanoidRootPart") then
                local distance = (checkPlayer.Character.HumanoidRootPart.Position - droppedItem.Position).Magnitude

                -- Se jogador está próximo o suficiente (5 studs)
                if distance <= 5 then
                    isBeingCollected = true
                    collectConnection:Disconnect()

                    -- Notifica o cliente que coletou o item
                    local collectEvent = remoteEvents:FindFirstChild("CollectDroppedItem")
                    if collectEvent then
                        collectEvent:FireClient(checkPlayer)
                    end

                    -- Efeito visual de coleta
                    local collectEffect = Instance.new("Explosion")
                    collectEffect.Position = droppedItem.Position
                    collectEffect.BlastRadius = 0
                    collectEffect.BlastPressure = 0
                    collectEffect.Visible = false
                    collectEffect.Parent = workspace

                    droppedItem:Destroy()
                    print("💎 Item dropado coletado por " .. checkPlayer.Name .. " (distância: " .. math.floor(distance) .. ")")
                    return
                end
            end
        end
    end)
    
    -- Remove após 30 segundos e limpa conexão
    spawn(function()
        wait(30)
        if collectConnection then
            collectConnection:Disconnect()
        end
        if droppedItem and droppedItem.Parent then
            droppedItem:Destroy()
            print("📦 Item dropado expirou após 30 segundos")
        end
    end)

    print("📦 Item dropado criado por " .. player.Name .. " na posição " .. tostring(adjustedPosition))
end

-- Conecta evento de drop
dropItemEvent.OnServerEvent:Connect(function(player, position)
    createDroppedItem(player, position)
end)

print("DropManager inicializado com sucesso!")

return true