-- DropManager.lua
-- Gerencia itens dropados no servidor para que todos vejam

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Debris = game:GetService("Debris")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local dropItemEvent = remoteEvents:WaitForChild("DropItem")

-- Função para criar item dropado no servidor
local function createDroppedItem(player, position)
    if not player.Character then return end
    
    -- Cria item no chão
    local droppedItem = Instance.new("Part")
    droppedItem.Name = "DroppedResource"
    droppedItem.Size = Vector3.new(2, 2, 2)
    droppedItem.BrickColor = BrickColor.new("Bright yellow")
    droppedItem.Material = Enum.Material.Neon
    droppedItem.CanCollide = true
    droppedItem.Anchored = false
    droppedItem.Shape = Enum.PartType.Ball
    droppedItem.Parent = workspace
    droppedItem.Position = position
    
    -- Adiciona efeito de brilho
    local light = Instance.new("PointLight")
    light.Color = Color3.new(1, 1, 0)
    light.Brightness = 2
    light.Range = 10
    light.Parent = droppedItem
    
    -- Adiciona texto flutuante
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Size = UDim2.new(0, 100, 0, 50)
    billboardGui.StudsOffset = Vector3.new(0, 3, 0)
    billboardGui.Parent = droppedItem
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "💎 RECURSO"
    textLabel.TextColor3 = Color3.new(1, 1, 1)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = billboardGui
    
    -- Sistema de coleta por toque
    local isBeingCollected = false
    local function onTouch(hit)
        if isBeingCollected then return end
        
        local hitCharacter = hit.Parent
        local hitHumanoid = hitCharacter:FindFirstChild("Humanoid")
        local hitPlayer = Players:GetPlayerFromCharacter(hitCharacter)
        
        if hitHumanoid and hitPlayer then
            isBeingCollected = true
            
            -- Notifica o cliente que coletou o item
            local collectEvent = remoteEvents:FindFirstChild("CollectDroppedItem")
            if collectEvent then
                collectEvent:FireClient(hitPlayer)
            end
            
            droppedItem:Destroy()
            print("💎 Item dropado coletado por " .. hitPlayer.Name)
        end
    end
    
    droppedItem.Touched:Connect(onTouch)
    
    -- Remove após 30 segundos
    Debris:AddItem(droppedItem, 30)
    
    print("📦 Item dropado criado por " .. player.Name)
end

-- Conecta evento de drop
dropItemEvent.OnServerEvent:Connect(function(player, position)
    createDroppedItem(player, position)
end)

print("DropManager inicializado com sucesso!")

return true