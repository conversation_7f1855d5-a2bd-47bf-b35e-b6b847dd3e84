-- DamageManager.lua
-- Gerencia o sistema de dano do servidor

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitFor<PERSON>hild("RemoteEvents")
local dealDamageEvent = remoteEvents:WaitForChild("DealDamage")

-- Aguarda PlayerShieldManager
local PlayerShieldManager = require(script.Parent.PlayerShieldManager)

-- Configurações de dano
local DAMAGE_COOLDOWN = 0.5 -- Cooldown entre danos do mesmo jogador
local lastDamageTime = {} -- Tabela para rastrear último dano por jogador

-- Função para verificar se jogadores são inimigos
local function arePlayersEnemies(attacker, target)
    if attacker == target then return false end
    
    -- Verifica se estão na mesma base
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and partner then
                local isAttackerInBase = (owner.Value == attacker) or (partner.Value == attacker)
                local isTargetInBase = (owner.Value == target) or (partner.Value == target)
                
                -- Se ambos estão na mesma base, são aliados
                if isAttackerInBase and isTargetInBase then
                    return false
                end
            end
        end
    end
    
    return true -- São inimigos se não estão na mesma base
end

-- Função para causar dano
local function dealDamage(attacker, target, damage)
    if not attacker or not target then return end
    if not target.Character or not target.Character:FindFirstChild("Humanoid") then return end
    
    -- Verifica se o alvo tem escudo
    if PlayerShieldManager.HasShield(target) then
        print("🛡️ " .. target.Name .. " está protegido por escudo - dano bloqueado!")
        return
    end
    
    -- Verifica cooldown
    local attackerKey = attacker.Name .. "_" .. target.Name
    local currentTime = tick()
    if lastDamageTime[attackerKey] and (currentTime - lastDamageTime[attackerKey]) < DAMAGE_COOLDOWN then
        return
    end
    
    -- Verifica se são inimigos
    if not arePlayersEnemies(attacker, target) then
        print("🛡️ " .. attacker.Name .. " tentou atacar aliado " .. target.Name .. " - bloqueado!")
        return
    end
    
    -- Causa dano
    local humanoid = target.Character.Humanoid
    humanoid.Health = math.max(0, humanoid.Health - damage)
    
    -- Atualiza cooldown
    lastDamageTime[attackerKey] = currentTime
    
    print("💥 " .. attacker.Name .. " causou " .. damage .. " de dano em " .. target.Name .. " (Vida: " .. humanoid.Health .. ")")
end

-- Conecta evento de dano
dealDamageEvent.OnServerEvent:Connect(function(attacker, target, damage)
    -- Se target é um jogador
    if target and target:IsA("Player") then
        dealDamage(attacker, target, damage or 10)
    -- Se target é um NPC (Character sem Player)
    elseif target and target:IsA("Model") and target:FindFirstChild("Humanoid") then
        local humanoid = target.Humanoid
        humanoid.Health = math.max(0, humanoid.Health - (damage or 10))
        print("💥 " .. attacker.Name .. " causou " .. (damage or 10) .. " de dano em NPC " .. target.Name .. " (Vida: " .. humanoid.Health .. ")")
    end
end)

-- Limpa cooldowns quando jogadores saem
Players.PlayerRemoving:Connect(function(player)
    for key, _ in pairs(lastDamageTime) do
        if key:find(player.Name) then
            lastDamageTime[key] = nil
        end
    end
end)

print("DamageManager inicializado com sucesso!")

return true