-- GameConfig.lua
-- Configurações centralizadas do jogo

local GameConfig = {}

-- Configurações das Bases
GameConfig.BASE_CONFIG = {
    MAX_BASES = 8,
    BASE_SIZE_DEFAULT = 100,
    BASE_SIZE_MIN = 10,
    BASE_SIZE_MAX = 500,
    BARRIER_HEIGHT_MIN = 15,
    BARRIER_HEIGHT_MAX = 40,
    PLATFORM_SIZE_MIN = 20,
    PLATFORM_SIZE_MAX = 60
}

-- Cores das Equipes (exatamente 8 cores para 8 bases)
GameConfig.TEAM_COLORS = {
    "Bright red",
    "Bright blue", 
    "Lime green",
    "New Yeller",
    "Bright violet",
    "Bright orange",
    "Hot pink",
    "Cyan"
}

-- Posições das Bases no Mapa (8 bases - reduzidas em 30%)
GameConfig.BASE_POSITIONS = {
    Vector3.new(175, 5, 175),
    Vector3.new(-175, 5, 175),
    Vector3.new(175, 5, -175),
    Vector3.new(-175, 5, -175),
    Vector3.new(245, 5, 0),
    Vector3.new(-245, 5, 0),
    Vector3.new(0, 5, 245),
    Vector3.new(0, 5, -245)
}

-- Configurações de Recursos
GameConfig.RESOURCE_CONFIG = {
    MAX_RESOURCES = 75,
    RESPAWN_TIME = 15,
    SHRINK_RATE = 0.02,
    MIN_SIZE_MULTIPLIER = 0.1,
    COLLECTION_SLOWDOWN = 0.3,
    DESPAWN_TIME = 60,  -- Objetos desaparecem após 1 minuto
    SPAWN_FREQUENCY = 5  -- Spawna novos objetos a cada 5 segundos
}

-- Configurações de Combate
GameConfig.COMBAT_CONFIG = {
    DAMAGE_AMOUNT = 25,
    WEAPON_RANGE = 200,
    FIRE_RATE = 0.5,
    BASE_ATTACK_DAMAGE = 1,
    DEATH_BASE_REDUCTION = 20
}

-- Configurações de Respawn
GameConfig.RESPAWN_CONFIG = {
    SOLO_TIME = 10,
    TEAM_TIME = 15
}

-- Configurações de Depósito
GameConfig.DEPOSIT_CONFIG = {
    BASE_SIZE_INCREASE = 15,
    BUILDING_MATERIALS_INCREASE = 10
}

-- Configurações da Barreira
GameConfig.BARRIER_CONFIG = {
    DAMAGE_PER_SECOND = 5,
    HEAL_PER_SECOND = 3,
    CHECK_INTERVAL = 1
}

-- Configurações de Construção
GameConfig.BUILDING_CONFIG = {
    WALL_COST = 1,
    TOWER_COST = 1,
    GENERATOR_COST = 1,
    DEPOT_COST = 1,
    TURRET_COST = 1,
    SHIELD_COST = 1
}

-- Tipos de Construções Disponíveis (expandido)
GameConfig.BUILDING_TYPES = {
    {
        name = "Turret",
        displayName = "Torreta Laser",
        cost = 1,
        size = Vector3.new(3, 4, 3),
        color = "Really black",
        material = Enum.Material.Metal,
        description = "Torreta automática que atira laser nos inimigos",
        icon = "🔴"
    },
    {
        name = "Wall",
        displayName = "Muro Defensivo",
        cost = 2,
        size = Vector3.new(8, 6, 2),
        color = "Dark stone grey",
        material = Enum.Material.Concrete,
        description = "Muro resistente que bloqueia ataques",
        icon = "🧱"
    },
    {
        name = "Generator",
        displayName = "Gerador de Energia",
        cost = 3,
        size = Vector3.new(4, 6, 4),
        color = "Bright yellow",
        material = Enum.Material.Neon,
        description = "Aumenta eficiência das outras construções",
        icon = "⚡"
    },
    {
        name = "Radar",
        displayName = "Radar de Detecção",
        cost = 2,
        size = Vector3.new(3, 8, 3),
        color = "Bright blue",
        material = Enum.Material.ForceField,
        description = "Detecta inimigos se aproximando",
        icon = "📡"
    },
    {
        name = "Shield",
        displayName = "Escudo Protetor",
        cost = 4,
        size = Vector3.new(6, 8, 6),
        color = "Cyan",
        material = Enum.Material.ForceField,
        description = "Cria barreira de energia protetora",
        icon = "🛡️"
    }
}

return GameConfig