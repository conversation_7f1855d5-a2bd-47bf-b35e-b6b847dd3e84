-- RemoveDefaultUI.lua
-- Remove completamente a UI padrão do Roblox

local StarterGui = game:GetService("StarterGui")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer

-- Função para remover UI padrão
local function removeDefaultUI()
    -- Remove a barra de vida padrão (canto superior direito)
    StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.Health, false)

    -- Remove outros elementos que podem interferir
    StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.PlayerList, false)

    print("✅ UI padrão do Roblox removida!")
end

-- Função para remover barras de vida dos personagens
local function removeCharacterHealthBars()
    if player.Character then
        local humanoid = player.Character:FindFirstChild("Humanoid")
        if humanoid then
            -- Remove a barra de vida da cabeça
            humanoid.DisplayDistanceType = Enum.HumanoidDisplayDistanceType.None
            humanoid.HealthDisplayType = Enum.HumanoidHealthDisplayType.AlwaysOff
            humanoid.NameDisplayDistance = 0
            humanoid.HealthDisplayDistance = 0
        end

        -- Remove qualquer GUI de vida que possa existir na cabeça
        local head = player.Character:FindFirstChild("Head")
        if head then
            for _, child in pairs(head:GetChildren()) do
                if child:IsA("BillboardGui") and (child.Name:lower():find("health") or child.Name:lower():find("vida")) then
                    child:Destroy()
                end
            end
        end
    end
end

-- Função para remover barras de vida de outros jogadores
local function removeOtherPlayersHealthBars()
    for _, otherPlayer in pairs(Players:GetPlayers()) do
        if otherPlayer ~= player and otherPlayer.Character then
            local humanoid = otherPlayer.Character:FindFirstChild("Humanoid")
            if humanoid then
                humanoid.DisplayDistanceType = Enum.HumanoidDisplayDistanceType.None
                humanoid.HealthDisplayType = Enum.HumanoidHealthDisplayType.AlwaysOff
                humanoid.NameDisplayDistance = 0
                humanoid.HealthDisplayDistance = 0
            end

            -- Remove GUIs de vida da cabeça
            local head = otherPlayer.Character:FindFirstChild("Head")
            if head then
                for _, child in pairs(head:GetChildren()) do
                    if child:IsA("BillboardGui") and (child.Name:lower():find("health") or child.Name:lower():find("vida")) then
                        child:Destroy()
                    end
                end
            end
        end
    end
end

-- Remove UI padrão imediatamente
removeDefaultUI()

-- Remove barras de vida quando o personagem spawna
local function onCharacterAdded()
    wait(1) -- Aguarda um pouco para garantir que tudo carregou
    removeCharacterHealthBars()
    removeOtherPlayersHealthBars()
end

-- Conecta eventos
if player.Character then
    onCharacterAdded()
end

player.CharacterAdded:Connect(onCharacterAdded)

-- Monitora outros jogadores
Players.PlayerAdded:Connect(function(otherPlayer)
    otherPlayer.CharacterAdded:Connect(function()
        wait(1)
        removeOtherPlayersHealthBars()
    end)
end)

-- Loop contínuo para garantir que as barras não reapareçam
spawn(function()
    while true do
        wait(5) -- Verifica a cada 5 segundos
        removeCharacterHealthBars()
        removeOtherPlayersHealthBars()
    end
end)

print("✅ Sistema completo de remoção de UI padrão ativo!")