-- BaseBoundaryDamage.lua
-- Sistema melhorado de dano ao entrar em bases inimigas

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")

-- Cria eventos se não existirem
local playerEnteredEnemyBase = remoteEvents:FindFirstChild("PlayerEnteredEnemyBase")
if not playerEnteredEnemyBase then
    playerEnteredEnemyBase = Instance.new("RemoteEvent")
    playerEnteredEnemyBase.Name = "PlayerEnteredEnemyBase"
    playerEnteredEnemyBase.Parent = remoteEvents
end

local playerLeftEnemyBase = remoteEvents:FindFirstChild("PlayerLeftEnemyBase")
if not playerLeftEnemyBase then
    playerLeftEnemyBase = Instance.new("RemoteEvent")
    playerLeftEnemyBase.Name = "PlayerLeftEnemyBase"
    playerLeftEnemyBase.Parent = remoteEvents
end

local BaseBoundaryDamage = {}

-- Configurações
local BOUNDARY_DAMAGE = 5 -- Dano por segundo
local DAMAGE_INTERVAL = 1 -- Intervalo entre danos (segundos)
local BOUNDARY_TOLERANCE = 0.5 -- Tolerância para detecção da linha (studs)

-- Tabelas para rastrear jogadores
local playersInEnemyBases = {} -- [player] = {base, lastDamageTime, notified}

-- Função para verificar se um jogador é dono ou aliado de uma base
local function isPlayerOwnerOrAlly(player, base)
    if not base then return false end
    
    local owner = base:FindFirstChild("Owner")
    local partner = base:FindFirstChild("Partner")
    
    local isOwner = owner and owner.Value == player
    local isPartner = partner and partner.Value == player
    
    return isOwner or isPartner
end

-- Função para verificar se jogador está dentro da linha da base (melhorada)
local function isPlayerInBaseBoundary(player, base)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local barrier = base:FindFirstChild("Barrier")
    if not barrier then return false end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    local barrierPosition = barrier.Position
    local barrierRadius = barrier.Size.X / 2
    
    -- Calcula distância horizontal (ignora Y)
    local horizontalDistance = math.sqrt(
        (playerPosition.X - barrierPosition.X)^2 + 
        (playerPosition.Z - barrierPosition.Z)^2
    )
    
    -- Verifica se está dentro ou tocando a linha da barreira
    return horizontalDistance <= (barrierRadius + BOUNDARY_TOLERANCE)
end

-- Função para verificar se jogador está na linha da base (exatamente na borda)
local function isPlayerOnBaseBoundaryLine(player, base)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local barrier = base:FindFirstChild("Barrier")
    if not barrier then return false end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    local barrierPosition = barrier.Position
    local barrierRadius = barrier.Size.X / 2
    
    -- Calcula distância horizontal
    local horizontalDistance = math.sqrt(
        (playerPosition.X - barrierPosition.X)^2 + 
        (playerPosition.Z - barrierPosition.Z)^2
    )
    
    -- Verifica se está na linha da barreira (com tolerância pequena)
    return math.abs(horizontalDistance - barrierRadius) <= BOUNDARY_TOLERANCE
end

-- Função para aplicar dano de invasão
local function applyBoundaryDamage(player)
    if not player.Character or not player.Character:FindFirstChild("Humanoid") then
        return
    end
    
    local humanoid = player.Character.Humanoid
    humanoid.Health = math.max(0, humanoid.Health - BOUNDARY_DAMAGE)
    
    print("⚔️ " .. player.Name .. " sofreu " .. BOUNDARY_DAMAGE .. " de dano por invasão! Vida: " .. humanoid.Health)
end

-- Função para verificar todos os jogadores
local function checkAllPlayers()
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local currentlyInEnemyBase = false
            local currentEnemyBase = nil
            
            -- Verifica todas as bases
            for _, base in ipairs(workspace:GetChildren()) do
                if base.Name:match("Base_") then
                    local owner = base:FindFirstChild("Owner")
                    
                    -- Só verifica bases que têm dono
                    if owner and owner.Value then
                        -- Verifica se não é a própria base
                        if not isPlayerOwnerOrAlly(player, base) then
                            -- Verifica se está dentro da linha da base
                            if isPlayerInBaseBoundary(player, base) then
                                currentlyInEnemyBase = true
                                currentEnemyBase = base
                                break
                            end
                        end
                    end
                end
            end
            
            local wasInEnemyBase = playersInEnemyBases[player] ~= nil
            
            if currentlyInEnemyBase and not wasInEnemyBase then
                -- Jogador entrou em base inimiga
                playersInEnemyBases[player] = {
                    base = currentEnemyBase,
                    lastDamageTime = tick(),
                    notified = false
                }
                
                -- Notifica entrada
                playerEnteredEnemyBase:FireClient(player, currentEnemyBase.Name)
                print("🚨 " .. player.Name .. " entrou na " .. currentEnemyBase.Name .. " inimiga!")
                
            elseif not currentlyInEnemyBase and wasInEnemyBase then
                -- Jogador saiu de base inimiga
                playerLeftEnemyBase:FireClient(player)
                playersInEnemyBases[player] = nil
                print("✅ " .. player.Name .. " saiu da base inimiga.")
                
            elseif currentlyInEnemyBase and wasInEnemyBase then
                -- Jogador ainda está em base inimiga - aplica dano
                local playerData = playersInEnemyBases[player]
                local currentTime = tick()
                
                if currentTime - playerData.lastDamageTime >= DAMAGE_INTERVAL then
                    applyBoundaryDamage(player)
                    playerData.lastDamageTime = currentTime
                end
            end
        end
    end
end

-- Função para mostrar efeito de cura na base própria
local function checkBaseHealing()
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") and player.Character:FindFirstChild("Humanoid") then
            local humanoid = player.Character.Humanoid
            
            -- Só cura se não estiver com vida cheia
            if humanoid.Health < humanoid.MaxHealth then
                -- Verifica se está na própria base
                for _, base in ipairs(workspace:GetChildren()) do
                    if base.Name:match("Base_") then
                        if isPlayerOwnerOrAlly(player, base) and isPlayerInBaseBoundary(player, base) then
                            -- Cura o jogador
                            humanoid.Health = math.min(humanoid.MaxHealth, humanoid.Health + 2) -- 2 HP por segundo
                            
                            -- Envia efeito de cura para o cliente
                            local showHealingEffect = remoteEvents:FindFirstChild("ShowHealingEffect")
                            if not showHealingEffect then
                                showHealingEffect = Instance.new("RemoteEvent")
                                showHealingEffect.Name = "ShowHealingEffect"
                                showHealingEffect.Parent = remoteEvents
                            end
                            showHealingEffect:FireClient(player)
                            break
                        end
                    end
                end
            end
        end
    end
end

-- Loop principal
spawn(function()
    while true do
        wait(0.1) -- Verifica a cada 0.1 segundos para detecção precisa
        checkAllPlayers()
    end
end)

-- Loop de cura
spawn(function()
    while true do
        wait(1) -- Cura a cada segundo
        checkBaseHealing()
    end
end)

-- Limpa dados quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    playersInEnemyBases[player] = nil
end)

print("BaseBoundaryDamage inicializado com sucesso!")

return BaseBoundaryDamage