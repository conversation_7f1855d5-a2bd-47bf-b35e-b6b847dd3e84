-- RemoteEvents.lua
-- Cria todos os RemoteEvents necessários

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Cria pasta para organizar eventos
local remoteEventsFolder = Instance.new("Folder")
remoteEventsFolder.Name = "RemoteEvents"
remoteEventsFolder.Parent = ReplicatedStorage

-- Sistema de Convites
local sendInvite = Instance.new("RemoteEvent")
sendInvite.Name = "SendInvite"
sendInvite.Parent = remoteEventsFolder

local respondInvite = Instance.new("RemoteEvent")
respondInvite.Name = "RespondInvite"
respondInvite.Parent = remoteEventsFolder

-- Sistema de Coleta
local startCollecting = Instance.new("RemoteEvent")
startCollecting.Name = "StartCollecting"
startCollecting.Parent = remoteEventsFolder

local stopCollecting = Instance.new("RemoteEvent")
stopCollecting.Name = "StopCollecting"
stopCollecting.Parent = remoteEventsFolder

local collectComplete = Instance.new("RemoteEvent")
collectComplete.Name = "CollectComplete"
collectComplete.Parent = remoteEventsFolder

-- Sistema de Combate
local fireCombatGun = Instance.new("RemoteEvent")
fireCombatGun.Name = "FireCombatGun"
fireCombatGun.Parent = remoteEventsFolder

local playerDamaged = Instance.new("RemoteEvent")
playerDamaged.Name = "PlayerDamaged"
playerDamaged.Parent = remoteEventsFolder

-- Sistema de Ataque à Base
local attackBase = Instance.new("RemoteEvent")
attackBase.Name = "AttackBase"
attackBase.Parent = remoteEventsFolder

-- Sistema de Depósito
local depositResource = Instance.new("RemoteEvent")
depositResource.Name = "DepositResource"
depositResource.Parent = remoteEventsFolder

-- Sistema de Construção
local requestBuild = Instance.new("RemoteEvent")
requestBuild.Name = "RequestBuild"
requestBuild.Parent = remoteEventsFolder

local buildResponse = Instance.new("RemoteEvent")
buildResponse.Name = "BuildResponse"
buildResponse.Parent = remoteEventsFolder

-- Atualizações de UI
local updateBaseInfo = Instance.new("RemoteEvent")
updateBaseInfo.Name = "UpdateBaseInfo"
updateBaseInfo.Parent = remoteEventsFolder

local updateInviteUI = Instance.new("RemoteEvent")
updateInviteUI.Name = "UpdateInviteUI"
updateInviteUI.Parent = remoteEventsFolder

-- Sistema de Construção
local openBuildMenu = Instance.new("RemoteEvent")
openBuildMenu.Name = "OpenBuildMenu"
openBuildMenu.Parent = remoteEventsFolder

local closeBuildMenu = Instance.new("RemoteEvent")
closeBuildMenu.Name = "CloseBuildMenu"
closeBuildMenu.Parent = remoteEventsFolder

local buildStructure = Instance.new("RemoteEvent")
buildStructure.Name = "BuildStructure"
buildStructure.Parent = remoteEventsFolder

local moveStructure = Instance.new("RemoteEvent")
moveStructure.Name = "MoveStructure"
moveStructure.Parent = remoteEventsFolder

local deleteStructure = Instance.new("RemoteEvent")
deleteStructure.Name = "DeleteStructure"
deleteStructure.Parent = remoteEventsFolder

-- Sistema de Dano
local dealDamage = Instance.new("RemoteEvent")
dealDamage.Name = "DealDamage"
dealDamage.Parent = remoteEventsFolder

-- Sistema de Itens Dropados
local dropItem = Instance.new("RemoteEvent")
dropItem.Name = "DropItem"
dropItem.Parent = remoteEventsFolder

local collectDroppedItem = Instance.new("RemoteEvent")
collectDroppedItem.Name = "CollectDroppedItem"
collectDroppedItem.Parent = remoteEventsFolder

-- Sistema de Sincronização de Efeitos Visuais
local showLaserEffect = Instance.new("RemoteEvent")
showLaserEffect.Name = "ShowLaserEffect"
showLaserEffect.Parent = remoteEventsFolder

local showBeamEffect = Instance.new("RemoteEvent")
showBeamEffect.Name = "ShowBeamEffect"
showBeamEffect.Parent = remoteEventsFolder

print("RemoteEvents criados com sucesso!")
return true