-- DepositManager.lua
-- Gerencia o depósito de recursos nas bases

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda dependências
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local depositResource = remoteEvents:WaitForChild("DepositResource")
local updateBaseInfo = remoteEvents:WaitForChild("UpdateBaseInfo")

local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))
local BaseController = require(script.Parent.BaseController)

local DepositManager = {}

-- Função para obter a base de um jogador
local function getPlayerBase(player)
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                return base
            end
        end
    end
    return nil
end

-- Função para verificar se um jogador está carregando recurso
local function isCarryingResource(player)
    if not player.Character then return false, 0 end
    
    local carryingValue = player.Character:FindFirstChild("CarregandoRecurso")
    local resourceValue = player.Character:FindFirstChild("ResourceValue")
    
    if carryingValue and carryingValue.Value and resourceValue then
        return true, resourceValue.Value
    end
    
    return false, 0
end

-- Função para remover estado de carregamento
local function removeCarryingState(player)
    if not player.Character then return end
    
    local carryingValue = player.Character:FindFirstChild("CarregandoRecurso")
    local resourceValue = player.Character:FindFirstChild("ResourceValue")
    
    if carryingValue then carryingValue:Destroy() end
    if resourceValue then resourceValue:Destroy() end
    
    -- Restaura velocidade normal
    local humanoid = player.Character:FindFirstChild("Humanoid")
    if humanoid then
        humanoid.WalkSpeed = 32  -- Dobrado de 16 para 32
    end
end

-- Função para processar depósito
local function processDeposit(player, base, resourceValue)
    -- Aumenta BaseSize da equipe em +15 (conforme especificação)
    local baseSizeValue = base:FindFirstChild("BaseSize")
    if baseSizeValue then
        baseSizeValue.Value = math.min(GameConfig.BASE_CONFIG.BASE_SIZE_MAX, 
            baseSizeValue.Value + GameConfig.DEPOSIT_CONFIG.BASE_SIZE_INCREASE)
    end
    
    -- Aumenta BuildingMaterials da equipe em +10 (conforme especificação)
    local buildingMaterialsValue = base:FindFirstChild("BuildingMaterials")
    if buildingMaterialsValue then
        buildingMaterialsValue.Value = buildingMaterialsValue.Value + GameConfig.DEPOSIT_CONFIG.BUILDING_MATERIALS_INCREASE
    end
    
    -- Remove o estado de 'carregando' e a lentidão do jogador
    removeCarryingState(player)
    
    -- Dispara uma atualização visual para a base
    BaseController.UpdateBase(base)
    
    -- Atualiza UI do jogador
    -- Converte o nome da base para mostrar a cor
    local teamColorValue = base:FindFirstChild("TeamColor")
    local displayName = base.Name
    
    if teamColorValue then
        local colorName = teamColorValue.Value
        local friendlyColorNames = {
            ["Bright red"] = "Vermelha",
            ["Bright blue"] = "Azul", 
            ["Lime green"] = "Verde",
            ["New Yeller"] = "Amarela",
            ["Bright violet"] = "Roxa",
            ["Bright orange"] = "Laranja",
            ["Hot pink"] = "Rosa",
            ["Cyan"] = "Ciano"
        }
        
        displayName = "Base_" .. (friendlyColorNames[colorName] or colorName) .. " (Sua Base)"
    end
    
    updateBaseInfo:FireClient(player, displayName, baseSizeValue.Value, buildingMaterialsValue.Value)
    
    print(player.Name .. " depositou recurso na base " .. base.Name .. 
          ". BaseSize: " .. baseSizeValue.Value .. ", Materials: " .. buildingMaterialsValue.Value)
end

-- Função para verificar se jogador está tocando o bloco de depósito
local function isPlayerOnDepositBlock(player, base)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end

    local spawnLocation = base:FindFirstChild("SpawnLocation")
    if not spawnLocation then return false end

    local playerPos = player.Character.HumanoidRootPart.Position
    local blockPos = spawnLocation.Position
    local blockSize = spawnLocation.Size

    -- Verifica se o jogador está em cima do bloco (com margem)
    local xInRange = math.abs(playerPos.X - blockPos.X) <= (blockSize.X / 2 + 2)
    local zInRange = math.abs(playerPos.Z - blockPos.Z) <= (blockSize.Z / 2 + 2)
    local yInRange = playerPos.Y >= (blockPos.Y + blockSize.Y / 2 - 1) and
                     playerPos.Y <= (blockPos.Y + blockSize.Y / 2 + 10)

    return xInRange and zInRange and yInRange
end

-- Loop para verificar depósitos automáticos
local function checkAutoDeposits()
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local isCarrying, resourceValue = isCarryingResource(player)

            if isCarrying then
                local base = getPlayerBase(player)

                if base then
                    -- Verifica se o jogador está dentro da barreira da sua equipe OU em cima do bloco azul
                    local isInBarrier = BaseController.IsPlayerInBarrier(player, base)
                    local isOnDepositBlock = isPlayerOnDepositBlock(player, base)

                    if isInBarrier or isOnDepositBlock then
                        processDeposit(player, base, resourceValue)
                    end
                end
            end
        end
    end
end

-- Loop de verificação a cada segundo
spawn(function()
    while true do
        wait(1)
        checkAutoDeposits()
    end
end)

-- Manipula depósitos manuais (se necessário)
depositResource.OnServerEvent:Connect(function(player)
    local isCarrying, resourceValue = isCarryingResource(player)
    
    if isCarrying then
        local base = getPlayerBase(player)
        
        if base then
            processDeposit(player, base, resourceValue)
        end
    end
end)

print("DepositManager inicializado com sucesso!")

return DepositManager