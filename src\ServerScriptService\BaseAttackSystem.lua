-- BaseAttackSystem.lua
-- Sistema completo de ataque a bases com regeneração e destruição

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local BaseAttackSystem = {}

-- Configurações
local MAX_BASE_HEALTH = 100
local ATTACK_DAMAGE = 2 -- Dan<PERSON> por ataque
local REGENERATION_RATE = 0.5 -- HP por segundo de regeneração
local MIN_HEALTH_TO_DESTROY = 10 -- Saúde mínima antes de destruir
local REGENERATION_DELAY = 5 -- Segundos sem ataque para começar regeneração

-- Tabelas para rastrear estado das bases
local baseHealths = {}
local baseLastAttackTime = {}
local baseAttackers = {}

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")

-- Cria eventos se não existirem
local updateBaseHealth = remoteEvents:FindFirstChild("UpdateBaseHealth")
if not updateBaseHealth then
    updateBaseHealth = Instance.new("RemoteEvent")
    updateBaseHealth.Name = "UpdateBaseHealth"
    updateBaseHealth.Parent = remoteEvents
end

local baseDestroyed = remoteEvents:FindFirstChild("BaseDestroyed")
if not baseDestroyed then
    baseDestroyed = Instance.new("RemoteEvent")
    baseDestroyed.Name = "BaseDestroyed"
    baseDestroyed.Parent = remoteEvents
end

-- Função para inicializar saúde das bases
local function initializeBaseHealth(base)
    if not baseHealths[base] then
        baseHealths[base] = MAX_BASE_HEALTH
        baseLastAttackTime[base] = 0
        baseAttackers[base] = {}
    end
end

-- Função para atualizar barreira e boundary em tempo real
local function updateBaseVisuals(base, healthPercentage)
    local barrier = base:FindFirstChild("Barrier")
    if barrier then
        -- Atualiza tamanho da barreira baseado na saúde
        local minSize = 20 -- Tamanho mínimo
        local maxSize = 100 -- Tamanho máximo
        local newSize = minSize + (maxSize - minSize) * healthPercentage
        
        barrier.Size = Vector3.new(newSize, barrier.Size.Y, newSize)
        
        -- Atualiza transparência (mais transparente = mais danificada)
        barrier.Transparency = 0.7 + (0.3 * (1 - healthPercentage))
    end
    
    -- Atualiza boundary lines
    local boundaryFolder = base:FindFirstChild("BoundaryLines")
    if boundaryFolder then
        local barrierRadius = (barrier and barrier.Size.X / 2) or 50
        local basePlatform = base:FindFirstChild("BasePlatform")
        local groundY = basePlatform and (basePlatform.Position.Y + basePlatform.Size.Y/2 + 0.5) or 5
        
        -- Recalcula posições das linhas
        local barrierCenter = barrier and barrier.Position or base:FindFirstChild("BasePlatform").Position
        local corners = {
            Vector3.new(barrierCenter.X - barrierRadius, groundY, barrierCenter.Z - barrierRadius),
            Vector3.new(barrierCenter.X + barrierRadius, groundY, barrierCenter.Z - barrierRadius),
            Vector3.new(barrierCenter.X + barrierRadius, groundY, barrierCenter.Z + barrierRadius),
            Vector3.new(barrierCenter.X - barrierRadius, groundY, barrierCenter.Z + barrierRadius)
        }
        
        -- Atualiza cada linha
        for i = 1, 4 do
            local line = boundaryFolder:FindFirstChild("BoundaryLine" .. i)
            if line then
                local startCorner = corners[i]
                local endCorner = corners[i % 4 + 1]
                local midPoint = (startCorner + endCorner) / 2
                local distance = (endCorner - startCorner).Magnitude
                
                line.Size = Vector3.new(1, 0.5, distance)
                line.Position = midPoint
                line.CFrame = CFrame.lookAt(midPoint, endCorner)
                
                -- Atualiza cor baseado na saúde
                if healthPercentage > 0.7 then
                    line.BrickColor = BrickColor.new("Lime green")
                elseif healthPercentage > 0.3 then
                    line.BrickColor = BrickColor.new("New Yeller")
                else
                    line.BrickColor = BrickColor.new("Bright red")
                end
            end
        end
    end
end

-- Função para criar notificação de vida da base
local function showBaseHealthNotification(base, health, maxHealth)
    local healthPercentage = health / maxHealth
    local healthText = math.floor(health) .. "/" .. maxHealth
    
    -- Envia para todos os jogadores próximos
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local basePlatform = base:FindFirstChild("BasePlatform")
            if basePlatform then
                local distance = (player.Character.HumanoidRootPart.Position - basePlatform.Position).Magnitude
                if distance < 150 then -- Dentro de 150 studs
                    updateBaseHealth:FireClient(player, base.Name, healthText, healthPercentage)
                end
            end
        end
    end
end

-- Função para atacar base
function BaseAttackSystem.AttackBase(attacker, base)
    if not base or not attacker then return false end
    
    -- Verifica se é uma base válida
    local owner = base:FindFirstChild("Owner")
    if not owner or not owner.Value then return false end
    
    -- Verifica se não é a própria base
    local partner = base:FindFirstChild("Partner")
    if (owner.Value == attacker) or (partner and partner.Value == attacker) then
        return false
    end
    
    -- Inicializa saúde se necessário
    initializeBaseHealth(base)
    
    -- Aplica dano
    baseHealths[base] = math.max(0, baseHealths[base] - ATTACK_DAMAGE)
    baseLastAttackTime[base] = tick()
    
    -- Adiciona atacante à lista
    baseAttackers[base][attacker] = tick()
    
    local currentHealth = baseHealths[base]
    local healthPercentage = currentHealth / MAX_BASE_HEALTH
    
    -- Atualiza visuais em tempo real
    updateBaseVisuals(base, healthPercentage)
    
    -- Mostra notificação de vida
    showBaseHealthNotification(base, currentHealth, MAX_BASE_HEALTH)
    
    print("💥 " .. attacker.Name .. " atacou " .. base.Name .. "! Vida: " .. math.floor(currentHealth) .. "/" .. MAX_BASE_HEALTH)
    
    -- Verifica se a base foi destruída
    if currentHealth <= MIN_HEALTH_TO_DESTROY then
        BaseAttackSystem.DestroyBase(base)
        return true
    end
    
    return true
end

-- Função para destruir base
function BaseAttackSystem.DestroyBase(base)
    local owner = base:FindFirstChild("Owner")
    local partner = base:FindFirstChild("Partner")
    
    -- Notifica destruição
    baseDestroyed:FireAllClients(base.Name)
    
    -- Reset da base
    if owner then owner.Value = nil end
    if partner then partner.Value = nil end
    
    -- Reset visual
    local barrier = base:FindFirstChild("Barrier")
    if barrier then
        barrier.Size = Vector3.new(100, barrier.Size.Y, 100)
        barrier.Transparency = 0.7
    end
    
    -- Reset boundary lines
    updateBaseVisuals(base, 1.0)
    
    -- Reset saúde
    baseHealths[base] = MAX_BASE_HEALTH
    baseLastAttackTime[base] = 0
    baseAttackers[base] = {}
    
    print("💥 " .. base.Name .. " foi destruída e resetada!")
end

-- Função para regenerar bases
local function regenerateBases()
    for base, health in pairs(baseHealths) do
        if base and base.Parent then
            local timeSinceLastAttack = tick() - baseLastAttackTime[base]
            
            -- Só regenera se não foi atacada recentemente
            if timeSinceLastAttack >= REGENERATION_DELAY and health < MAX_BASE_HEALTH then
                local newHealth = math.min(MAX_BASE_HEALTH, health + REGENERATION_RATE)
                baseHealths[base] = newHealth
                
                local healthPercentage = newHealth / MAX_BASE_HEALTH
                
                -- Atualiza visuais
                updateBaseVisuals(base, healthPercentage)
                
                -- Mostra notificação de regeneração
                if math.floor(newHealth) % 10 == 0 then -- A cada 10 HP
                    showBaseHealthNotification(base, newHealth, MAX_BASE_HEALTH)
                end
            end
        else
            -- Remove bases inválidas
            baseHealths[base] = nil
            baseLastAttackTime[base] = nil
            baseAttackers[base] = nil
        end
    end
end

-- Inicializa bases existentes
for i = 1, 8 do
    local base = workspace:FindFirstChild("Base_" .. i)
    if base then
        initializeBaseHealth(base)
        updateBaseVisuals(base, 1.0) -- Saúde completa inicialmente
    end
end

-- Loop de regeneração
spawn(function()
    while true do
        wait(1) -- A cada segundo
        regenerateBases()
    end
end)

-- Limpa atacantes inativos
spawn(function()
    while true do
        wait(30) -- A cada 30 segundos
        local currentTime = tick()
        
        for base, attackers in pairs(baseAttackers) do
            for attacker, lastAttackTime in pairs(attackers) do
                if currentTime - lastAttackTime > 60 then -- 1 minuto sem atacar
                    attackers[attacker] = nil
                end
            end
        end
    end
end)

-- Conecta evento de ataque
local attackBaseEvent = remoteEvents:FindFirstChild("AttackBase")
if not attackBaseEvent then
    attackBaseEvent = Instance.new("RemoteEvent")
    attackBaseEvent.Name = "AttackBase"
    attackBaseEvent.Parent = remoteEvents
end

attackBaseEvent.OnServerEvent:Connect(function(player, base)
    BaseAttackSystem.AttackBase(player, base)
end)

print("BaseAttackSystem inicializado com sucesso!")

return BaseAttackSystem