-- BaseInteractionUI.lua
-- Gerencia a interação com o objeto retangular da base para abrir menu de construções

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local openBuildMenu = remoteEvents:WaitForChild("OpenBuildMenu")

-- Variáveis globais
local interactionGui = nil
local currentInteractionObject = nil
local isNearInteractionObject = false
local isHoldingE = false
local holdStartTime = 0
local HOLD_TIME_REQUIRED = 0.1 -- Apenas um toque rápido

-- Cria a UI de interação
local function createInteractionGUI()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "BaseInteractionGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Frame da interação
    local interactionFrame = Instance.new("Frame")
    interactionFrame.Name = "InteractionFrame"
    interactionFrame.Size = UDim2.new(0, 200, 0, 80)
    interactionFrame.Position = UDim2.new(0.5, -100, 0.5, -40)
    interactionFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    interactionFrame.BackgroundTransparency = 0.3
    interactionFrame.BorderSizePixel = 2
    interactionFrame.BorderColor3 = Color3.new(1, 1, 1)
    interactionFrame.Visible = false
    interactionFrame.Parent = screenGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = interactionFrame
    
    -- Texto da interação
    local interactionText = Instance.new("TextLabel")
    interactionText.Name = "InteractionText"
    interactionText.Size = UDim2.new(1, 0, 0.6, 0)
    interactionText.Position = UDim2.new(0, 0, 0, 0)
    interactionText.BackgroundTransparency = 1
    interactionText.Text = "🔨 Pressione [E] para abrir menu"
    interactionText.TextColor3 = Color3.new(1, 1, 1)
    interactionText.TextScaled = true
    interactionText.Font = Enum.Font.SourceSansBold
    interactionText.Parent = interactionFrame
    
    -- Barra de progresso
    local progressBarBG = Instance.new("Frame")
    progressBarBG.Name = "ProgressBarBG"
    progressBarBG.Size = UDim2.new(1, -20, 0, 15)
    progressBarBG.Position = UDim2.new(0, 10, 0.6, 5)
    progressBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    progressBarBG.BorderSizePixel = 1
    progressBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    progressBarBG.Parent = interactionFrame
    
    local progressBar = Instance.new("Frame")
    progressBar.Name = "ProgressBar"
    progressBar.Size = UDim2.new(0, 0, 1, 0)
    progressBar.Position = UDim2.new(0, 0, 0, 0)
    progressBar.BackgroundColor3 = Color3.new(0, 1, 0)
    progressBar.BorderSizePixel = 0
    progressBar.Parent = progressBarBG
    
    return screenGui
end

-- Função para verificar se o jogador é dono ou aliado da base
local function isPlayerOwnerOrAlly(base)
    if not base then return false end
    
    local owner = base:FindFirstChild("Owner")
    local partner = base:FindFirstChild("Partner")
    
    local isOwner = owner and owner.Value == player
    local isPartner = partner and partner.Value == player
    
    if isOwner or isPartner then
        print("🏠 Jogador é dono/aliado da " .. base.Name)
        return true
    end
    
    return false
end

-- Função para encontrar o objeto de interação próximo (atualizada para botão)
local function findNearbyInteractionObject()
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return nil
    end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    local closestObject = nil
    local closestDistance = math.huge
    
    -- Procura por bases
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            -- Verifica se o jogador é dono ou aliado desta base
            if isPlayerOwnerOrAlly(base) then
                -- Procura pelo botão de interação
                local interactionButton = base:FindFirstChild("InteractionButton")
                if interactionButton then
                    local distance = (playerPosition - interactionButton.Position).Magnitude
                    if distance < 8 and distance < closestDistance then -- 8 studs de distância
                        closestDistance = distance
                        closestObject = interactionButton
                        print("🎯 Botão de interação encontrado na " .. base.Name)
                    end
                end
            end
        end
    end
    
    return closestObject
end

-- Função para atualizar a UI de interação
local function updateInteractionUI()
    if not interactionGui then
        interactionGui = createInteractionGUI()
    end
    
    local interactionFrame = interactionGui.InteractionFrame
    local nearbyObject = findNearbyInteractionObject()
    
    if nearbyObject then
        if nearbyObject ~= currentInteractionObject then
            currentInteractionObject = nearbyObject
            isNearInteractionObject = true
            print("🎯 Mostrando UI de interação para " .. nearbyObject.Name)
        end
        
        interactionFrame.Visible = true
        
        -- Posiciona a UI acima do objeto
        local camera = workspace.CurrentCamera
        local objectPosition = nearbyObject.Position + Vector3.new(0, nearbyObject.Size.Y/2 + 3, 0)
        local screenPosition, onScreen = camera:WorldToScreenPoint(objectPosition)
        
        if onScreen then
            interactionFrame.Position = UDim2.new(0, screenPosition.X - 100, 0, screenPosition.Y - 40)
        end
        
    elseif not nearbyObject and isNearInteractionObject then
        currentInteractionObject = nil
        isNearInteractionObject = false
        isHoldingE = false
        holdStartTime = 0
        interactionFrame.Visible = false
        
        -- Reset da barra de progresso
        local progressBar = interactionFrame.ProgressBarBG.ProgressBar
        progressBar.Size = UDim2.new(0, 0, 1, 0)
    end
    
    -- Atualiza posição da UI se ainda estiver próximo
    if isNearInteractionObject and currentInteractionObject then
        local camera = workspace.CurrentCamera
        local objectPosition = currentInteractionObject.Position + Vector3.new(0, currentInteractionObject.Size.Y/2 + 3, 0)
        local screenPosition, onScreen = camera:WorldToScreenPoint(objectPosition)
        
        if onScreen then
            interactionFrame.Position = UDim2.new(0, screenPosition.X - 100, 0, screenPosition.Y - 40)
            interactionFrame.Visible = true
        else
            interactionFrame.Visible = false
        end
    end
end

-- Função para atualizar a barra de progresso
local function updateProgressBar()
    if not interactionGui or not isHoldingE then return end
    
    local progressBar = interactionGui.InteractionFrame.ProgressBarBG.ProgressBar
    local elapsed = tick() - holdStartTime
    local progress = math.min(elapsed / HOLD_TIME_REQUIRED, 1)
    
    progressBar.Size = UDim2.new(progress, 0, 1, 0)
    
    -- Muda cor baseado no progresso
    if progress < 0.5 then
        progressBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
    elseif progress < 0.8 then
        progressBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
    else
        progressBar.BackgroundColor3 = Color3.new(0, 1, 0) -- Verde
    end
    
    -- Abre menu quando completa
    if progress >= 1 then
        isHoldingE = false
        holdStartTime = 0
        progressBar.Size = UDim2.new(0, 0, 1, 0)
        
        -- Abre o menu de construções
        openBuildMenu:FireServer()
        print("🔨 Menu de construções aberto via interação!")
    end
end

-- Controle de entrada
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.E and isNearInteractionObject and not isHoldingE then
        isHoldingE = true
        holdStartTime = tick()
        print("🔨 Iniciando interação com objeto da base...")
    end
end)

UserInputService.InputEnded:Connect(function(input, gameProcessed)
    if input.KeyCode == Enum.KeyCode.E and isHoldingE then
        isHoldingE = false
        holdStartTime = 0
        
        -- Reset da barra de progresso
        if interactionGui then
            local progressBar = interactionGui.InteractionFrame.ProgressBarBG.ProgressBar
            progressBar.Size = UDim2.new(0, 0, 1, 0)
        end
        
        print("🔨 Interação cancelada.")
    end
end)

-- Loop principal
local function mainLoop()
    local connection
    connection = RunService.Heartbeat:Connect(function()
        if not player.Character then
            connection:Disconnect()
            return
        end
        
        updateInteractionUI()
        
        if isHoldingE then
            updateProgressBar()
        end
    end)
end

-- Inicializa quando o jogador spawna
if player.Character then
    mainLoop()
end

player.CharacterAdded:Connect(function()
    task.wait(1)
    mainLoop()
end)

print("BaseInteractionUI carregado com sucesso!")