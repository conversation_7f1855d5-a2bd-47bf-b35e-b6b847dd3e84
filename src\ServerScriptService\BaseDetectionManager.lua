-- BaseDetectionManager.lua
-- Detecta quando jogadores entram/saem de bases e envia notificações

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local showNotification = remoteEvents:WaitForChild("ShowNotification")

-- Tabela para rastrear estado dos jogadores
local playerStates = {}

-- Função para verificar se jogadores são inimigos
local function arePlayersEnemies(player1, player2)
    if not player1 or not player2 then return false end
    
    -- Encontra bases dos jogadores
    local player1Base = nil
    local player2Base = nil
    
    for i = 1, 8 do
        local base = workspace:FindFirstChild("Base_" .. i)
        if base then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            
            if (owner and owner.Value == player1) or (partner and partner.Value == player1) then
                player1Base = base
            end
            if (owner and owner.Value == player2) or (partner and partner.Value == player2) then
                player2Base = base
            end
        end
    end
    
    -- Se não estão na mesma base, são inimigos
    return player1Base ~= player2Base
end

-- Função para verificar se jogador está em uma base
local function getPlayerBaseStatus(player)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return nil, nil, nil
    end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    
    for i = 1, 8 do
        local base = workspace:FindFirstChild("Base_" .. i)
        if base then
            local barrier = base:FindFirstChild("Barrier")
            if barrier then
                local distance = (playerPosition - barrier.Position).Magnitude
                local barrierRadius = barrier.Size.X / 2
                
                if distance <= barrierRadius then
                    local owner = base:FindFirstChild("Owner")
                    local partner = base:FindFirstChild("Partner")
                    
                    local isOwnBase = (owner and owner.Value == player) or (partner and partner.Value == player)
                    local baseOwner = nil
                    
                    if owner and owner.Value then
                        baseOwner = owner.Value
                    end
                    
                    return base, isOwnBase, baseOwner
                end
            end
        end
    end
    
    return nil, false, nil
end

-- Função para aplicar cura na própria base
local function applyBaseHealing(player)
    if not player.Character or not player.Character:FindFirstChild("Humanoid") then
        return
    end
    
    local humanoid = player.Character.Humanoid
    if humanoid.Health < humanoid.MaxHealth then
        humanoid.Health = math.min(humanoid.MaxHealth, humanoid.Health + 2) -- Cura 2 HP por segundo
        
        -- Efeito visual de cura
        local healEffect = Instance.new("Part")
        healEffect.Name = "HealEffect"
        healEffect.Size = Vector3.new(1, 1, 1)
        healEffect.Position = player.Character.HumanoidRootPart.Position + Vector3.new(0, 3, 0)
        healEffect.BrickColor = BrickColor.new("Bright green")
        healEffect.Material = Enum.Material.Neon
        healEffect.CanCollide = false
        healEffect.Anchored = true
        healEffect.Shape = Enum.PartType.Ball
        healEffect.Parent = workspace
        
        -- Remove efeito após 1 segundo
        game:GetService("Debris"):AddItem(healEffect, 1)
        
        -- Animação do efeito
        spawn(function()
            for i = 1, 10 do
                healEffect.Transparency = i / 10
                healEffect.Size = healEffect.Size + Vector3.new(0.2, 0.2, 0.2)
                wait(0.1)
            end
        end)
    end
end

-- Loop principal de detecção
local function detectionLoop()
    for _, player in pairs(Players:GetPlayers()) do
        if player.Character then
            local currentBase, isOwnBase, baseOwner = getPlayerBaseStatus(player)
            local previousState = playerStates[player.Name] or {}
            
            -- Verifica mudanças de estado
            if currentBase and not previousState.inBase then
                -- Entrou em uma base
                if isOwnBase then
                    showNotification:FireClient(player, "🏠 Você entrou na sua base! Curando...", "SUCCESS", 3)
                else
                    local ownerName = baseOwner and baseOwner.Name or "inimiga"
                    showNotification:FireClient(player, "⚠️ Você entrou na base de " .. ownerName .. "!", "WARNING", 4)
                    
                    -- Notifica o dono da base se estiver online
                    if baseOwner and baseOwner.Parent then
                        showNotification:FireClient(baseOwner, "🚨 " .. player.Name .. " invadiu sua base!", "ERROR", 5)
                    end
                end
                
                playerStates[player.Name] = {
                    inBase = true,
                    isOwnBase = isOwnBase,
                    baseName = currentBase.Name,
                    baseOwner = baseOwner
                }
                
            elseif not currentBase and previousState.inBase then
                -- Saiu de uma base
                if previousState.isOwnBase then
                    showNotification:FireClient(player, "🏠 Você saiu da sua base.", "INFO", 2)
                else
                    local ownerName = previousState.baseOwner and previousState.baseOwner.Name or "inimiga"
                    showNotification:FireClient(player, "⚠️ Você saiu da base de " .. ownerName .. ".", "INFO", 2)
                end
                
                playerStates[player.Name] = {
                    inBase = false,
                    isOwnBase = false,
                    baseName = nil,
                    baseOwner = nil
                }
                
            elseif currentBase and previousState.inBase and isOwnBase then
                -- Está na própria base - aplica cura
                applyBaseHealing(player)
            end
        end
    end
end

-- Limpa estado quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    playerStates[player.Name] = nil
end)

-- Inicia loop de detecção
spawn(function()
    while true do
        wait(1) -- Verifica a cada segundo
        detectionLoop()
    end
end)

print("BaseDetectionManager inicializado com sucesso!")

return true
