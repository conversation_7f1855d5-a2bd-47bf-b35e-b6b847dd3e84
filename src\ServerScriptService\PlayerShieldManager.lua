-- PlayerShieldManager.lua
-- Gerencia escudo de proteção para novos jogadores

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local PlayerShieldManager = {}

-- Tabela para rastrear jogadores com escudo
local playersWithShield = {}

-- Função para criar efeito visual do escudo
local function createShieldEffect(character)
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return nil end
    
    -- Cria esfera de proteção
    local shield = Instance.new("Part")
    shield.Name = "ProtectionShield"
    shield.Size = Vector3.new(8, 8, 8)
    shield.Position = humanoidRootPart.Position
    shield.BrickColor = BrickColor.new("Bright blue")
    shield.Material = Enum.Material.ForceField
    shield.Transparency = 0.7
    shield.CanCollide = false
    shield.Anchored = true
    shield.Shape = Enum.PartType.Ball
    shield.Parent = character
    
    -- Adiciona luz
    local light = Instance.new("PointLight")
    light.Color = Color3.new(0, 0.5, 1)
    light.Brightness = 2
    light.Range = 15
    light.Parent = shield
    
    -- Efeito de rotação
    local rotateConnection = RunService.Heartbeat:Connect(function()
        if shield and shield.Parent then
            shield.CFrame = shield.CFrame * CFrame.Angles(0, 0.05, 0)
        else
            rotateConnection:Disconnect()
        end
    end)
    
    -- Atualiza posição do escudo
    local updateConnection = RunService.Heartbeat:Connect(function()
        if shield and shield.Parent and humanoidRootPart and humanoidRootPart.Parent then
            shield.Position = humanoidRootPart.Position
        else
            updateConnection:Disconnect()
        end
    end)
    
    return shield, {rotateConnection, updateConnection}
end

-- Função para criar GUI do escudo
local function createShieldGUI(player, timeLeft)
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "ShieldGUI"
    screenGui.Parent = player.PlayerGui
    
    -- Frame do escudo
    local shieldFrame = Instance.new("Frame")
    shieldFrame.Size = UDim2.new(0, 200, 0, 60)
    shieldFrame.Position = UDim2.new(0.5, -100, 0, 50)
    shieldFrame.BackgroundColor3 = Color3.new(0, 0.3, 0.8)
    shieldFrame.BackgroundTransparency = 0.2
    shieldFrame.BorderSizePixel = 2
    shieldFrame.BorderColor3 = Color3.new(0, 0.5, 1)
    shieldFrame.Parent = screenGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = shieldFrame
    
    -- Texto do escudo
    local shieldText = Instance.new("TextLabel")
    shieldText.Size = UDim2.new(1, 0, 0.6, 0)
    shieldText.Position = UDim2.new(0, 0, 0, 0)
    shieldText.BackgroundTransparency = 1
    shieldText.Text = "🛡️ ESCUDO ATIVO"
    shieldText.TextColor3 = Color3.new(1, 1, 1)
    shieldText.TextScaled = true
    shieldText.Font = Enum.Font.SourceSansBold
    shieldText.Parent = shieldFrame
    
    -- Contador de tempo
    local timeText = Instance.new("TextLabel")
    timeText.Size = UDim2.new(1, 0, 0.4, 0)
    timeText.Position = UDim2.new(0, 0, 0.6, 0)
    timeText.BackgroundTransparency = 1
    timeText.Text = timeLeft .. "s restantes"
    timeText.TextColor3 = Color3.new(0.8, 0.8, 1)
    timeText.TextScaled = true
    timeText.Font = Enum.Font.Gotham
    timeText.Parent = shieldFrame
    
    return screenGui, timeText
end

-- Função para ativar escudo
function PlayerShieldManager.ActivateShield(player)
    if playersWithShield[player] then return end -- Já tem escudo
    
    local character = player.Character
    if not character then return end
    
    -- Marca jogador como protegido
    playersWithShield[player] = {
        startTime = tick(),
        duration = 10,
        active = true
    }
    
    -- Cria efeito visual
    local shield, connections = createShieldEffect(character)
    
    -- Cria GUI
    local shieldGUI, timeText = createShieldGUI(player, 10)
    
    -- Atualiza contador
    local updateConnection = RunService.Heartbeat:Connect(function()
        local shieldData = playersWithShield[player]
        if not shieldData or not shieldData.active then
            updateConnection:Disconnect()
            return
        end
        
        local elapsed = tick() - shieldData.startTime
        local timeLeft = math.max(0, shieldData.duration - elapsed)
        
        if timeText and timeText.Parent then
            timeText.Text = math.ceil(timeLeft) .. "s restantes"
        end
        
        if timeLeft <= 0 then
            PlayerShieldManager.DeactivateShield(player)
            updateConnection:Disconnect()
        end
    end)
    
    -- Armazena referências para limpeza
    playersWithShield[player].shield = shield
    playersWithShield[player].connections = connections
    playersWithShield[player].gui = shieldGUI
    playersWithShield[player].updateConnection = updateConnection
    
    print("🛡️ Escudo ativado para " .. player.Name .. " por 10 segundos")
end

-- Função para desativar escudo
function PlayerShieldManager.DeactivateShield(player)
    local shieldData = playersWithShield[player]
    if not shieldData then return end
    
    shieldData.active = false
    
    -- Remove efeito visual
    if shieldData.shield and shieldData.shield.Parent then
        shieldData.shield:Destroy()
    end
    
    -- Desconecta conexões
    if shieldData.connections then
        for _, connection in ipairs(shieldData.connections) do
            if connection then
                connection:Disconnect()
            end
        end
    end
    
    if shieldData.updateConnection then
        shieldData.updateConnection:Disconnect()
    end
    
    -- Remove GUI
    if shieldData.gui and shieldData.gui.Parent then
        shieldData.gui:Destroy()
    end
    
    -- Remove da tabela
    playersWithShield[player] = nil
    
    print("🛡️ Escudo removido de " .. player.Name)
end

-- Função para verificar se jogador tem escudo
function PlayerShieldManager.HasShield(player)
    local shieldData = playersWithShield[player]
    return shieldData and shieldData.active
end

-- Conecta aos jogadores
Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function()
        wait(2) -- Aguarda o jogador carregar completamente
        PlayerShieldManager.ActivateShield(player)
    end)
end)

-- Remove escudo quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    PlayerShieldManager.DeactivateShield(player)
end)

-- Monitora quando jogadores reivindicam bases (remove escudo)
workspace.ChildAdded:Connect(function(child)
    if child.Name:match("Base_") then
        wait(0.5)
        local owner = child:FindFirstChild("Owner")
        if owner then
            owner.Changed:Connect(function()
                if owner.Value and PlayerShieldManager.HasShield(owner.Value) then
                    PlayerShieldManager.DeactivateShield(owner.Value)
                    print("🏠 Escudo removido de " .. owner.Value.Name .. " ao reivindicar base")
                end
            end)
        end
    end
end)

-- Configura para bases existentes
for i = 1, 8 do
    local base = workspace:FindFirstChild("Base_" .. i)
    if base then
        local owner = base:FindFirstChild("Owner")
        if owner then
            owner.Changed:Connect(function()
                if owner.Value and PlayerShieldManager.HasShield(owner.Value) then
                    PlayerShieldManager.DeactivateShield(owner.Value)
                    print("🏠 Escudo removido de " .. owner.Value.Name .. " ao reivindicar base")
                end
            end)
        end
    end
end

print("PlayerShieldManager inicializado com sucesso!")

return PlayerShieldManager