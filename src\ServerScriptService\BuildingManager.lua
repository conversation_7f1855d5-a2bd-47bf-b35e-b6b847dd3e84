-- BuildingManager.lua
-- Gerencia o sistema de construção de bases

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

-- Aguarda dependências
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))

local BuildingManager = {}

-- Variáveis globais
local baseBuildings = {} -- Mapeia base para suas construções
local playerEditMode = {} -- Jogadores em modo de edição

-- Cria RemoteEvents se não existirem
local function createRemoteEvents()
    local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
    if not remoteEvents then
        remoteEvents = Instance.new("Folder")
        remoteEvents.Name = "RemoteEvents"
        remoteEvents.Parent = ReplicatedStorage
    end
    
    local events = {
        "OpenBuildMenu",
        "CloseBuildMenu", 
        "BuildStructure",
        "MoveStructure",
        "DeleteStructure",
        "UpdateBaseInfo"
    }
    
    for _, eventName in ipairs(events) do
        if not remoteEvents:FindFirstChild(eventName) then
            local remoteEvent = Instance.new("RemoteEvent")
            remoteEvent.Name = eventName
            remoteEvent.Parent = remoteEvents
        end
    end
end

-- Verifica se o jogador está na sua base
local function isPlayerInOwnBase(player)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("Jogador " .. player.Name .. " não tem character ou HumanoidRootPart")
        return false, nil
    end
    
    local playerPosition = player.Character.HumanoidRootPart.Position
    print("Posição do jogador " .. player.Name .. ":", playerPosition)
    
    -- Procura por todas as bases no workspace
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")
            local barrier = base:FindFirstChild("Barrier")
            
            print("Verificando base:", base.Name)
            print("Owner:", owner and owner.Value and owner.Value.Name or "nenhum")
            print("Partner:", partner and partner.Value and partner.Value.Name or "nenhum")
            
            -- Verifica se o jogador é dono ou parceiro
            local isOwner = owner and owner.Value == player
            local isPartner = partner and partner.Value == player
            
            print("É owner:", isOwner, "É partner:", isPartner)
            
            if (isOwner or isPartner) and barrier then
                -- Verifica se está dentro da barreira
                local distance = (playerPosition - barrier.Position).Magnitude
                local barrierRadius = barrier.Size.X / 2
                
                print("Distância da barreira:", distance, "Raio da barreira:", barrierRadius)
                
                if distance <= barrierRadius then
                    print("Jogador " .. player.Name .. " está na sua base " .. base.Name)
                    return true, base
                end
            end
        end
    end
    
    print("Jogador " .. player.Name .. " não está na sua base")
    return false, nil
end

-- Verifica se há espaço para construir
local function hasSpaceForBuilding(base, buildingType, position)
    local barrier = base:FindFirstChild("Barrier")
    if not barrier then return false end
    
    local barrierRadius = barrier.Size.X / 2
    local barrierCenter = barrier.Position
    
    -- Verifica se a construção ficará dentro da barreira
    local buildingSize = buildingType.size
    local corners = {
        position + Vector3.new(buildingSize.X/2, 0, buildingSize.Z/2),
        position + Vector3.new(-buildingSize.X/2, 0, buildingSize.Z/2),
        position + Vector3.new(buildingSize.X/2, 0, -buildingSize.Z/2),
        position + Vector3.new(-buildingSize.X/2, 0, -buildingSize.Z/2)
    }
    
    for _, corner in ipairs(corners) do
        local distance = (corner - barrierCenter).Magnitude
        if distance > barrierRadius then
            return false
        end
    end
    
    -- Verifica colisão com outras construções
    local buildings = baseBuildings[base] or {}
    for _, building in ipairs(buildings) do
        if building and building.Parent then
            local distance = (building.Position - position).Magnitude
            local minDistance = (building.Size.Magnitude + buildingSize.Magnitude) / 2
            if distance < minDistance then
                return false
            end
        end
    end
    
    return true
end

-- Cria uma construção
local function createBuilding(base, buildingType, position)
    local building = Instance.new("Part")
    building.Name = buildingType.name .. "_Building"
    building.Size = buildingType.size
    building.Position = position
    building.BrickColor = BrickColor.new(buildingType.color)
    building.Material = buildingType.material
    building.Anchored = true
    building.Parent = workspace
    
    -- Aplica cor da equipe se a base tiver uma
    local teamColorValue = base:FindFirstChild("TeamColor")
    if teamColorValue then
        local teamColor = BrickColor.new(teamColorValue.Value)
        building.BrickColor = teamColor
    end
    
    -- Adiciona identificação da base
    local baseId = Instance.new("ObjectValue")
    baseId.Name = "BaseOwner"
    baseId.Value = base
    baseId.Parent = building
    
    -- Adiciona tipo da construção
    local buildingTypeValue = Instance.new("StringValue")
    buildingTypeValue.Name = "BuildingType"
    buildingTypeValue.Value = buildingType.name
    buildingTypeValue.Parent = building
    
    -- Adiciona à lista de construções da base
    if not baseBuildings[base] then
        baseBuildings[base] = {}
    end
    table.insert(baseBuildings[base], building)
    
    -- Efeito visual de construção
    local light = Instance.new("PointLight")
    light.Brightness = 2
    light.Range = 10
    light.Color = Color3.new(0, 1, 0)
    light.Parent = building
    
    spawn(function()
        wait(2)
        if light and light.Parent then
            light:Destroy()
        end
    end)
    
    print("Construção " .. buildingType.displayName .. " criada na base " .. base.Name)
    return building
end

-- Remove construções que ficaram fora da barreira
local function checkBuildingsInBarrier(base)
    local barrier = base:FindFirstChild("Barrier")
    if not barrier then return end
    
    local barrierRadius = barrier.Size.X / 2
    local barrierCenter = barrier.Position
    local buildings = baseBuildings[base] or {}
    
    for i = #buildings, 1, -1 do
        local building = buildings[i]
        if building and building.Parent then
            local distance = (building.Position - barrierCenter).Magnitude
            if distance > barrierRadius then
                -- Construção ficou fora da barreira, destruir
                print("Construção " .. building.Name .. " destruída por estar fora da barreira")
                building:Destroy()
                table.remove(buildings, i)
            end
        else
            table.remove(buildings, i)
        end
    end
end

-- Eventos dos RemoteEvents
local function setupRemoteEvents()
    local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
    
    -- Abrir menu de construção
    remoteEvents.OpenBuildMenu.OnServerEvent:Connect(function(player)
        print("Jogador " .. player.Name .. " tentou abrir menu de construção")
        local inBase, base = isPlayerInOwnBase(player)
        print("Está na base:", inBase, "Base:", base and base.Name or "nenhuma")
        
        if inBase then
            playerEditMode[player] = base
            
            -- Envia informações da base para o cliente
            local buildingMaterials = base:FindFirstChild("BuildingMaterials")
            local baseSize = base:FindFirstChild("BaseSize")
            local teamColorValue = base:FindFirstChild("TeamColor")
            
            -- Converte o nome da base para mostrar a cor
            local displayName = base.Name
            if teamColorValue then
                local colorName = teamColorValue.Value
                local friendlyColorNames = {
                    ["Bright red"] = "Vermelha",
                    ["Bright blue"] = "Azul", 
                    ["Lime green"] = "Verde",
                    ["New Yeller"] = "Amarela",
                    ["Bright violet"] = "Roxa",
                    ["Bright orange"] = "Laranja",
                    ["Hot pink"] = "Rosa",
                    ["Cyan"] = "Ciano"
                }
                
                displayName = "Base_" .. (friendlyColorNames[colorName] or colorName) .. " (Sua Base)"
            end
            
            print("Enviando info da base - Materiais:", buildingMaterials and buildingMaterials.Value or 0)
            
            remoteEvents.UpdateBaseInfo:FireClient(player, {
                baseName = displayName,
                buildingMaterials = buildingMaterials and buildingMaterials.Value or 0,
                baseSize = baseSize and baseSize.Value or 100,
                buildingTypes = GameConfig.BUILDING_TYPES
            })
        else
            print("Jogador não está na sua base")
        end
    end)
    
    -- Fechar menu de construção
    remoteEvents.CloseBuildMenu.OnServerEvent:Connect(function(player)
        playerEditMode[player] = nil
    end)
    
    -- Construir estrutura
    remoteEvents.BuildStructure.OnServerEvent:Connect(function(player, buildingTypeName, position)
        local base = playerEditMode[player]
        if not base then return end
        
        -- Encontra o tipo de construção
        local buildingType = nil
        for _, bType in ipairs(GameConfig.BUILDING_TYPES) do
            if bType.name == buildingTypeName then
                buildingType = bType
                break
            end
        end
        
        if not buildingType then return end
        
        -- Verifica materiais
        local buildingMaterials = base:FindFirstChild("BuildingMaterials")
        if not buildingMaterials or buildingMaterials.Value < buildingType.cost then
            return
        end
        
        -- Verifica espaço
        if not hasSpaceForBuilding(base, buildingType, position) then
            return
        end
        
        -- Deduz materiais e cria construção
        buildingMaterials.Value = buildingMaterials.Value - buildingType.cost
        createBuilding(base, buildingType, position)
        
        -- Atualiza informações no cliente
        local teamColorValue = base:FindFirstChild("TeamColor")
        local displayName = base.Name
        if teamColorValue then
            local colorName = teamColorValue.Value
            local friendlyColorNames = {
                ["Bright red"] = "Vermelha",
                ["Bright blue"] = "Azul", 
                ["Lime green"] = "Verde",
                ["New Yeller"] = "Amarela",
                ["Bright violet"] = "Roxa",
                ["Bright orange"] = "Laranja",
                ["Hot pink"] = "Rosa",
                ["Cyan"] = "Ciano"
            }
            
            displayName = "Base_" .. (friendlyColorNames[colorName] or colorName) .. " (Sua Base)"
        end
        
        remoteEvents.UpdateBaseInfo:FireClient(player, {
            baseName = displayName,
            buildingMaterials = buildingMaterials.Value,
            baseSize = base:FindFirstChild("BaseSize").Value,
            buildingTypes = GameConfig.BUILDING_TYPES
        })
    end)
    
    -- Mover estrutura
    remoteEvents.MoveStructure.OnServerEvent:Connect(function(player, building, newPosition)
        local base = playerEditMode[player]
        if not base or not building then return end
        
        local baseOwner = building:FindFirstChild("BaseOwner")
        if not baseOwner or baseOwner.Value ~= base then return end
        
        local buildingTypeValue = building:FindFirstChild("BuildingType")
        if not buildingTypeValue then return end
        
        -- Encontra o tipo de construção
        local buildingType = nil
        for _, bType in ipairs(GameConfig.BUILDING_TYPES) do
            if bType.name == buildingTypeValue.Value then
                buildingType = bType
                break
            end
        end
        
        if not buildingType then return end
        
        -- Verifica se a nova posição é válida
        if hasSpaceForBuilding(base, buildingType, newPosition) then
            building.Position = newPosition
        end
    end)
    
    -- Deletar estrutura
    remoteEvents.DeleteStructure.OnServerEvent:Connect(function(player, building)
        local base = playerEditMode[player]
        if not base or not building then return end
        
        local baseOwner = building:FindFirstChild("BaseOwner")
        if not baseOwner or baseOwner.Value ~= base then return end
        
        -- Remove da lista de construções
        local buildings = baseBuildings[base] or {}
        for i, b in ipairs(buildings) do
            if b == building then
                table.remove(buildings, i)
                break
            end
        end
        
        building:Destroy()
    end)
end

-- Conecta ao evento de atualização de base para verificar construções
local function connectToBaseUpdates()
    -- Esta função será chamada pelo BaseController quando a base for atualizada
    function BuildingManager.CheckBuildingsInBarrier(base)
        checkBuildingsInBarrier(base)
    end
end

-- Inicialização
createRemoteEvents()
setupRemoteEvents()
connectToBaseUpdates()

print("BuildingManager inicializado com sucesso!")

return BuildingManager