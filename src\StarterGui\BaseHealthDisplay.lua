-- BaseHealthDisplay.lua
-- Sistema de exibição de vida das bases em tempo real

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:Wait<PERSON>or<PERSON>hil<PERSON>("PlayerGui")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local updateBaseHealth = remoteEvents:WaitForChild("UpdateBaseHealth")
local baseDestroyed = remoteEvents:WaitForChild("BaseDestroyed")

-- Tabela para rastrear GUIs de vida das bases
local baseHealthGUIs = {}

-- Função para criar GUI de vida da base
local function createBaseHealthGUI(baseName, healthText, healthPercentage)
    -- Remove GUI existente
    if baseHealthGUIs[baseName] then
        baseHealthGUIs[baseName]:Destroy()
    end
    
    -- Encontra a base no workspace
    local base = workspace:FindFirstChild(baseName)
    if not base then return end
    
    local basePlatform = base:FindFirstChild("BasePlatform")
    if not basePlatform then return end
    
    -- Cria BillboardGui na base
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "BaseHealthGUI"
    billboardGui.Size = UDim2.new(0, 200, 0, 60)
    billboardGui.StudsOffset = Vector3.new(0, 15, 0)
    billboardGui.Parent = basePlatform
    
    -- Frame principal
    local healthFrame = Instance.new("Frame")
    healthFrame.Size = UDim2.new(1, 0, 1, 0)
    healthFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    healthFrame.BackgroundTransparency = 0.3
    healthFrame.BorderSizePixel = 2
    healthFrame.BorderColor3 = Color3.new(1, 1, 1)
    healthFrame.Parent = billboardGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = healthFrame
    
    -- Título da base
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0.4, 0)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "🏠 " .. baseName
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = healthFrame
    
    -- Barra de vida background
    local healthBarBG = Instance.new("Frame")
    healthBarBG.Size = UDim2.new(0.9, 0, 0.25, 0)
    healthBarBG.Position = UDim2.new(0.05, 0, 0.45, 0)
    healthBarBG.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    healthBarBG.BorderSizePixel = 1
    healthBarBG.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
    healthBarBG.Parent = healthFrame
    
    local healthBarCorner = Instance.new("UICorner")
    healthBarCorner.CornerRadius = UDim.new(0, 4)
    healthBarCorner.Parent = healthBarBG
    
    -- Barra de vida
    local healthBar = Instance.new("Frame")
    healthBar.Name = "HealthBar"
    healthBar.Size = UDim2.new(healthPercentage, 0, 1, 0)
    healthBar.Position = UDim2.new(0, 0, 0, 0)
    healthBar.BorderSizePixel = 0
    healthBar.Parent = healthBarBG
    
    local healthBarCorner2 = Instance.new("UICorner")
    healthBarCorner2.CornerRadius = UDim.new(0, 4)
    healthBarCorner2.Parent = healthBar
    
    -- Cor da barra baseada na saúde
    if healthPercentage > 0.7 then
        healthBar.BackgroundColor3 = Color3.new(0, 0.8, 0) -- Verde
    elseif healthPercentage > 0.3 then
        healthBar.BackgroundColor3 = Color3.new(1, 1, 0) -- Amarelo
    else
        healthBar.BackgroundColor3 = Color3.new(1, 0, 0) -- Vermelho
    end
    
    -- Texto da vida
    local healthLabel = Instance.new("TextLabel")
    healthLabel.Size = UDim2.new(1, 0, 0.3, 0)
    healthLabel.Position = UDim2.new(0, 0, 0.7, 0)
    healthLabel.BackgroundTransparency = 1
    healthLabel.Text = "❤️ " .. healthText
    healthLabel.TextColor3 = Color3.new(1, 1, 1)
    healthLabel.TextScaled = true
    healthLabel.Font = Enum.Font.Gotham
    healthLabel.Parent = healthFrame
    
    -- Efeito de entrada
    healthFrame.Size = UDim2.new(0, 0, 0, 0)
    local tweenInfo = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
    local tween = TweenService:Create(healthFrame, tweenInfo, {
        Size = UDim2.new(1, 0, 1, 0)
    })
    tween:Play()
    
    baseHealthGUIs[baseName] = billboardGui
    
    -- Remove automaticamente após 5 segundos
    spawn(function()
        wait(5)
        if baseHealthGUIs[baseName] == billboardGui then
            local fadeInfo = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.In)
            local fadeTween = TweenService:Create(healthFrame, fadeInfo, {
                BackgroundTransparency = 1
            })
            local fadeTextTween = TweenService:Create(titleLabel, fadeInfo, {
                TextTransparency = 1
            })
            local fadeHealthTween = TweenService:Create(healthLabel, fadeInfo, {
                TextTransparency = 1
            })
            
            fadeTween:Play()
            fadeTextTween:Play()
            fadeHealthTween:Play()
            
            fadeTween.Completed:Connect(function()
                if baseHealthGUIs[baseName] == billboardGui then
                    billboardGui:Destroy()
                    baseHealthGUIs[baseName] = nil
                end
            end)
        end
    end)
    
    return billboardGui
end

-- Função para atualizar GUI existente
local function updateBaseHealthGUI(baseName, healthText, healthPercentage)
    local gui = baseHealthGUIs[baseName]
    if gui then
        local healthFrame = gui:FindFirstChild("Frame")
        if healthFrame then
            local healthBar = healthFrame:FindFirstChild("Frame"):FindFirstChild("HealthBar")
            local healthLabel = healthFrame:FindFirstChild("TextLabel", true)
            
            if healthBar then
                -- Anima mudança na barra de vida
                local tweenInfo = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
                local tween = TweenService:Create(healthBar, tweenInfo, {
                    Size = UDim2.new(healthPercentage, 0, 1, 0)
                })
                tween:Play()
                
                -- Atualiza cor
                if healthPercentage > 0.7 then
                    healthBar.BackgroundColor3 = Color3.new(0, 0.8, 0)
                elseif healthPercentage > 0.3 then
                    healthBar.BackgroundColor3 = Color3.new(1, 1, 0)
                else
                    healthBar.BackgroundColor3 = Color3.new(1, 0, 0)
                end
            end
            
            if healthLabel then
                healthLabel.Text = "❤️ " .. healthText
            end
        end
    else
        -- Cria nova GUI se não existir
        createBaseHealthGUI(baseName, healthText, healthPercentage)
    end
end

-- Função para mostrar notificação de base destruída
local function showBaseDestroyedNotification(baseName)
    -- Remove GUI de vida
    if baseHealthGUIs[baseName] then
        baseHealthGUIs[baseName]:Destroy()
        baseHealthGUIs[baseName] = nil
    end
    
    -- Cria notificação de destruição
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "BaseDestroyedGUI"
    screenGui.Parent = playerGui
    
    local notificationFrame = Instance.new("Frame")
    notificationFrame.Size = UDim2.new(0, 300, 0, 80)
    notificationFrame.Position = UDim2.new(0.5, -150, 0.3, 0)
    notificationFrame.BackgroundColor3 = Color3.new(1, 0, 0)
    notificationFrame.BackgroundTransparency = 0.1
    notificationFrame.BorderSizePixel = 3
    notificationFrame.BorderColor3 = Color3.new(1, 1, 1)
    notificationFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = notificationFrame
    
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0.6, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "💥 BASE DESTRUÍDA!"
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = notificationFrame
    
    local messageLabel = Instance.new("TextLabel")
    messageLabel.Size = UDim2.new(1, 0, 0.4, 0)
    messageLabel.Position = UDim2.new(0, 0, 0.6, 0)
    messageLabel.BackgroundTransparency = 1
    messageLabel.Text = baseName .. " foi destruída!"
    messageLabel.TextColor3 = Color3.new(0.9, 0.9, 0.9)
    messageLabel.TextScaled = true
    messageLabel.Font = Enum.Font.Gotham
    messageLabel.Parent = notificationFrame
    
    -- Remove após 3 segundos
    spawn(function()
        wait(3)
        if screenGui and screenGui.Parent then
            screenGui:Destroy()
        end
    end)
end

-- Conecta eventos
updateBaseHealth.OnClientEvent:Connect(function(baseName, healthText, healthPercentage)
    updateBaseHealthGUI(baseName, healthText, healthPercentage)
end)

baseDestroyed.OnClientEvent:Connect(function(baseName)
    showBaseDestroyedNotification(baseName)
end)

print("BaseHealthDisplay carregado com sucesso!")