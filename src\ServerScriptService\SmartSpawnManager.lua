-- SmartSpawnManager.lua
-- Gerencia spawn inteligente perto de bases não reivindicadas

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local SmartSpawnManager = {}

-- Função para encontrar bases não reivindicadas
local function getUnclaimedBases()
    local unclaimedBases = {}
    
    for i = 1, 8 do
        local baseName = "Base_" .. i
        local base = workspace:FindFirstChild(baseName)
        if base then
            local owner = base:FindFirstChild("Owner")
            if owner and not owner.Value then
                table.insert(unclaimedBases, base)
            end
        end
    end
    
    return unclaimedBases
end

-- Função para encontrar a base mais próxima de uma posição
local function findClosestBase(position, bases)
    local closestBase = nil
    local closestDistance = math.huge
    
    for _, base in ipairs(bases) do
        local basePlatform = base:FindFirstChild("BasePlatform")
        if basePlatform then
            local distance = (basePlatform.Position - position).Magnitude
            if distance < closestDistance then
                closestDistance = distance
                closestBase = base
            end
        end
    end
    
    return closestBase
end

-- Função para criar spawn temporário perto de uma base
local function createTempSpawn(base, player)
    local basePlatform = base:FindFirstChild("BasePlatform")
    if not basePlatform then return nil end
    
    -- Cria spawn temporário
    local tempSpawn = Instance.new("SpawnLocation")
    tempSpawn.Name = "TempSpawn_" .. player.Name
    tempSpawn.Size = Vector3.new(6, 1, 6)
    tempSpawn.Position = basePlatform.Position + Vector3.new(15, 3, 0) -- 15 studs da base
    tempSpawn.BrickColor = BrickColor.new("Bright green")
    tempSpawn.Material = Enum.Material.ForceField
    tempSpawn.Anchored = true
    tempSpawn.CanCollide = false
    tempSpawn.Transparency = 0.5
    tempSpawn.Parent = workspace
    
    -- Adiciona texto
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Size = UDim2.new(0, 200, 0, 50)
    billboardGui.StudsOffset = Vector3.new(0, 3, 0)
    billboardGui.Parent = tempSpawn
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "🏠 BASE DISPONÍVEL"
    textLabel.TextColor3 = Color3.new(0, 1, 0)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = billboardGui
    
    return tempSpawn
end

-- Função para configurar spawn do jogador
function SmartSpawnManager.SetupPlayerSpawn(player)
    local unclaimedBases = getUnclaimedBases()
    
    if #unclaimedBases > 0 then
        -- Escolhe uma base aleatória não reivindicada
        local randomBase = unclaimedBases[math.random(1, #unclaimedBases)]
        local tempSpawn = createTempSpawn(randomBase, player)
        
        if tempSpawn then
            player.RespawnLocation = tempSpawn
            print("🏠 " .. player.Name .. " será spawnado perto de " .. randomBase.Name)
            
            -- Remove spawn temporário após 30 segundos
            spawn(function()
                wait(30)
                if tempSpawn and tempSpawn.Parent then
                    tempSpawn:Destroy()
                end
            end)
            
            return true
        end
    end
    
    -- Se não há bases disponíveis, usa spawn central
    local centralSpawn = workspace:FindFirstChild("CentralSpawn")
    if centralSpawn then
        player.RespawnLocation = centralSpawn
        print("🎯 " .. player.Name .. " será spawnado no centro (sem bases disponíveis)")
    end
    
    return false
end

-- Função para configurar respawn na própria base
function SmartSpawnManager.SetupBaseRespawn(player, base)
    local spawnLocation = base:FindFirstChild("SpawnLocation")
    if spawnLocation then
        spawnLocation.Enabled = true
        player.RespawnLocation = spawnLocation
        print("🏠 " .. player.Name .. " agora respawna na sua base: " .. base.Name)
        return true
    end
    return false
end

-- Conecta aos jogadores
Players.PlayerAdded:Connect(function(player)
    -- Aguarda o jogador carregar
    player.CharacterAdded:Connect(function()
        wait(1) -- Aguarda um pouco
        
        -- Verifica se o jogador já tem uma base
        local playerBase = nil
        for i = 1, 8 do
            local base = workspace:FindFirstChild("Base_" .. i)
            if base then
                local owner = base:FindFirstChild("Owner")
                local partner = base:FindFirstChild("Partner")
                if (owner and owner.Value == player) or (partner and partner.Value == player) then
                    playerBase = base
                    break
                end
            end
        end
        
        if playerBase then
            -- Jogador já tem base, configura respawn na base
            SmartSpawnManager.SetupBaseRespawn(player, playerBase)
        else
            -- Jogador novo, configura spawn perto de base não reivindicada
            SmartSpawnManager.SetupPlayerSpawn(player)
        end
    end)
end)

-- Monitora quando jogadores reivindicam bases
workspace.ChildAdded:Connect(function(child)
    if child.Name:match("Base_") then
        wait(0.5)
        local owner = child:FindFirstChild("Owner")
        if owner then
            owner.Changed:Connect(function()
                if owner.Value then
                    SmartSpawnManager.SetupBaseRespawn(owner.Value, child)
                end
            end)
        end
    end
end)

-- Configura para bases existentes
for i = 1, 8 do
    local base = workspace:FindFirstChild("Base_" .. i)
    if base then
        local owner = base:FindFirstChild("Owner")
        if owner then
            owner.Changed:Connect(function()
                if owner.Value then
                    SmartSpawnManager.SetupBaseRespawn(owner.Value, base)
                end
            end)
        end
    end
end

print("SmartSpawnManager inicializado com sucesso!")

return SmartSpawnManager