-- AutoInit.server.lua
-- Script que inicializa automaticamente todo o jogo

local ServerStorage = game:GetService("ServerStorage")

print("🚀 AUTO-INICIALIZANDO JOGO DE ARENA...")

-- Aguarda um pouco para garantir que todos os scripts carregaram
wait(2)

-- 1. Cria ferramentas primeiro
print("1. Criando ferramentas...")
local success1 = pcall(function()
    require(ServerStorage.CreateToolsWithScripts)
end)

if success1 then
    print("✅ Ferramentas criadas")
else
    warn("❌ Erro ao criar ferramentas")
end

-- 2. Inicializa o jogo principal (mapa já existe no workspace)
print("2. Inicializando jogo principal...")
local success = pcall(function()
    -- Inicializa todos os sistemas necessários
    require(script.Parent.ResourceManager)
    require(script.Parent.BaseManager)
    require(script.Parent.BaseBoundaryManager)
    require(script.Parent.CombatManager)
    require(script.Parent.DamageManager)
    require(script.Parent.DropManager)
    require(script.Parent.PlayerShieldManager)
    require(script.Parent.SmartSpawnManager)
    require(script.Parent.BaseEffectsManager)
    require(script.Parent.BaseAttackSystem)
    require(script.Parent.DepositManager)
    require(script.Parent.BaseAttackManager)
    require(script.Parent.InviteManager)
    require(script.Parent.BuildingManager)
    require(script.Parent.VisualEffectsManager)
    require(script.Parent.BaseDetectionManager)
    require(script.Parent.TurretManager)
    require(script.Parent.ForceSpawnNearBases)
    require(script.Parent.ToolGiver)
    require(script.Parent.FlagOrientationManager)
end)

if success then
    print("✅ Jogo inicializado com sucesso")
else
    warn("❌ Erro ao inicializar jogo")
end

print("🎉 AUTO-INICIALIZAÇÃO COMPLETA!")
print("")
print("🎮 SEU JOGO DE ARENA ESTÁ PRONTO!")
print("📋 Pressione PLAY para testar todas as funcionalidades:")
print("• Reivindique bases tocando nos ClaimPads amarelos")
print("• Use as ferramentas CombatGun e CollectorGun")
print("• Forme duplas usando o sistema de convites")
print("• Colete recursos e deposite na sua base")
print("• Construa defesas pressionando 'B' na sua barreira")
print("• Ataque bases inimigas para destruí-las")
print("")
print("🏆 BOA SORTE NA ARENA!")