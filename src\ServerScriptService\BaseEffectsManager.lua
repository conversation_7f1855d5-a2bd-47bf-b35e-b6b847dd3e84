-- BaseEffectsManager.lua
-- Gerencia efeitos de cura na própria base e dano em bases inimigas

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local BaseEffectsManager = {}

-- Configurações
local HEAL_RATE = 2 -- HP por segundo na própria base
local DAMAGE_RATE = 5 -- HP por segundo em base inimiga
local CHECK_INTERVAL = 0.5 -- Verifica a cada 0.5 segundos

-- Tabelas para rastrear jogadores
local playersInOwnBase = {}
local playersInEnemyBase = {}

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")

-- Cria eventos se não existirem
local showBaseNotification = remoteEvents:FindFirstChild("ShowBaseNotification")
if not showBaseNotification then
    showBaseNotification = Instance.new("RemoteEvent")
    showBaseNotification.Name = "ShowBaseNotification"
    showBaseNotification.Parent = remoteEvents
end

local hideBaseNotification = remoteEvents:FindFirstChild("HideBaseNotification")
if not hideBaseNotification then
    hideBaseNotification = Instance.new("RemoteEvent")
    hideBaseNotification.Name = "HideBaseNotification"
    hideBaseNotification.Parent = remoteEvents
end

local showHealEffect = remoteEvents:FindFirstChild("ShowHealEffect")
if not showHealEffect then
    showHealEffect = Instance.new("RemoteEvent")
    showHealEffect.Name = "ShowHealEffect"
    showHealEffect.Parent = remoteEvents
end

-- Função para verificar se jogador é dono/aliado de uma base
local function isPlayerOwnerOrAlly(player, base)
    local owner = base:FindFirstChild("Owner")
    local partner = base:FindFirstChild("Partner")
    
    return (owner and owner.Value == player) or (partner and partner.Value == player)
end

-- Função para encontrar base do jogador (melhorada para incluir linha quadrada)
local function getPlayerBase(player, position)
    for i = 1, 8 do
        local base = workspace:FindFirstChild("Base_" .. i)
        if base then
            local barrier = base:FindFirstChild("Barrier")
            if barrier then
                local distance = (barrier.Position - position).Magnitude
                local radius = (barrier.Size.X / 2) + 5 -- Adiciona 5 studs de margem para incluir a linha
                
                if distance <= radius then
                    return base
                end
            end
        end
    end
    return nil
end

-- Função para aplicar cura
local function applyHealing(player)
    if not player.Character or not player.Character:FindFirstChild("Humanoid") then return end
    
    local humanoid = player.Character.Humanoid
    local maxHealth = humanoid.MaxHealth
    local currentHealth = humanoid.Health
    
    if currentHealth < maxHealth then
        local newHealth = math.min(maxHealth, currentHealth + HEAL_RATE * CHECK_INTERVAL)
        humanoid.Health = newHealth
        
        -- Envia efeito visual de cura
        showHealEffect:FireClient(player)
        
        print("💚 " .. player.Name .. " sendo curado na sua base (Vida: " .. math.floor(newHealth) .. "/" .. maxHealth .. ")")
    end
end

-- Função para aplicar dano de base inimiga
local function applyEnemyBaseDamage(player)
    if not player.Character or not player.Character:FindFirstChild("Humanoid") then return end
    
    -- Verifica se tem escudo (importa PlayerShieldManager se existir)
    local hasShield = false
    local PlayerShieldManager = script.Parent:FindFirstChild("PlayerShieldManager")
    if PlayerShieldManager then
        local shieldModule = require(PlayerShieldManager)
        hasShield = shieldModule.HasShield(player)
    end
    
    if hasShield then
        print("🛡️ " .. player.Name .. " protegido por escudo em base inimiga")
        return
    end
    
    local humanoid = player.Character.Humanoid
    local currentHealth = humanoid.Health
    
    if currentHealth > 0 then
        local newHealth = math.max(0, currentHealth - DAMAGE_RATE * CHECK_INTERVAL)
        humanoid.Health = newHealth
        
        print("💀 " .. player.Name .. " sofrendo dano em base inimiga (Vida: " .. math.floor(newHealth) .. ")")
    end
end

-- Função principal de verificação
local function checkPlayerBaseEffects()
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local position = player.Character.HumanoidRootPart.Position
            local currentBase = getPlayerBase(player, position)
            
            if currentBase then
                local isOwner = isPlayerOwnerOrAlly(player, currentBase)
                
                if isOwner then
                    -- Jogador na própria base
                    if not playersInOwnBase[player] then
                        playersInOwnBase[player] = true
                        playersInEnemyBase[player] = nil
                        print("🏠 " .. player.Name .. " entrou na sua base - iniciando cura")
                    end
                    
                    applyHealing(player)
                    
                else
                    -- Jogador em base inimiga
                    if not playersInEnemyBase[player] then
                        playersInEnemyBase[player] = true
                        playersInOwnBase[player] = nil
                        
                        -- Mostra notificação de perigo
                        showBaseNotification:FireClient(player, "⚠️ PERIGO - BASE INIMIGA", "Você está sofrendo dano!", Color3.new(1, 0, 0))
                        print("⚠️ " .. player.Name .. " entrou em base inimiga - iniciando dano")
                    end
                    
                    applyEnemyBaseDamage(player)
                end
            else
                -- Jogador fora de qualquer base
                local wasInOwnBase = playersInOwnBase[player]
                local wasInEnemyBase = playersInEnemyBase[player]
                
                if wasInOwnBase then
                    playersInOwnBase[player] = nil
                    print("🏠 " .. player.Name .. " saiu da sua base - parando cura")
                end
                
                if wasInEnemyBase then
                    playersInEnemyBase[player] = nil
                    hideBaseNotification:FireClient(player)
                    print("⚠️ " .. player.Name .. " saiu da base inimiga - parando dano")
                end
            end
        end
    end
end

-- Limpa dados quando jogador sai
Players.PlayerRemoving:Connect(function(player)
    playersInOwnBase[player] = nil
    playersInEnemyBase[player] = nil
end)

-- Inicia loop principal
spawn(function()
    while true do
        wait(CHECK_INTERVAL)
        checkPlayerBaseEffects()
    end
end)

print("BaseEffectsManager inicializado com sucesso!")

return BaseEffectsManager