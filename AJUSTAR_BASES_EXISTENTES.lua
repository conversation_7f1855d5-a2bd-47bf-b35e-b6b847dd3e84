-- AJUSTAR_BASES_EXISTENTES.lua
-- Execute este script UMA VEZ no Command Bar do Roblox Studio para ajustar as bases existentes
-- Melhora mastros, bandeiras, posições e componentes

local teamColors = {
    "Bright red",
    "Bright blue", 
    "Lime green",
    "New Yeller",
    "Bright violet",
    "Bright orange",
    "Hot pink",
    "Cyan"
}

-- Função para ajustar uma base
local function adjustBase(base, index)
    if not base or not base.Name:match("Base_") then return end
    
    print("🔧 Ajustando " .. base.Name)
    
    -- Encontra componentes existentes
    local basePlatform = base:FindFirstChild("BasePlatform")
    local coreTower = base:FindFirstChild("CoreTower")
    local barrier = base:FindFirstChild("Barrier")
    local claimPad = base:FindFirstChild("ClaimPad")
    local spawnLocation = base:FindFirstChild("SpawnLocation")
    
    if not basePlatform then
        print("❌ BasePlatform não encontrada em " .. base.Name)
        return
    end
    
    local basePosition = basePlatform.Position
    
    -- <PERSON>ju<PERSON> ou cria CoreTower (mastro maior)
    if coreTower then
        coreTower.Size = Vector3.new(3, 20, 3) -- Mastro mais alto
        coreTower.Position = basePosition + Vector3.new(0, 11, 0)
        coreTower.BrickColor = BrickColor.new("Dark stone grey")
        coreTower.Material = Enum.Material.Concrete
    else
        coreTower = Instance.new("Part")
        coreTower.Name = "CoreTower"
        coreTower.Size = Vector3.new(3, 20, 3)
        coreTower.Position = basePosition + Vector3.new(0, 11, 0)
        coreTower.BrickColor = BrickColor.new("Dark stone grey")
        coreTower.Material = Enum.Material.Concrete
        coreTower.Anchored = true
        coreTower.Parent = base
    end
    
    -- Remove bandeira antiga se existir
    local oldFlag = coreTower:FindFirstChild("Flag")
    if oldFlag then oldFlag:Destroy() end
    
    -- Cria bandeira nova e maior
    local flag = Instance.new("Part")
    flag.Name = "Flag"
    flag.Size = Vector3.new(0.3, 8, 6) -- Bandeira maior
    flag.Position = basePosition + Vector3.new(4, 15, 0) -- Mais alta
    flag.BrickColor = BrickColor.new(teamColors[index] or "Bright red")
    flag.Material = Enum.Material.Fabric
    flag.Anchored = true
    flag.Parent = coreTower
    
    -- Orienta bandeira para o centro do mapa
    local direction = (Vector3.new(0, flag.Position.Y, 0) - flag.Position).Unit
    local lookDirection = Vector3.new(direction.X, 0, direction.Z).Unit
    local angle = math.atan2(-lookDirection.X, -lookDirection.Z)
    flag.CFrame = CFrame.new(flag.Position) * CFrame.Angles(0, angle, 0)
    
    -- Ajusta barreira
    if barrier then
        barrier.Size = Vector3.new(100, 25, 100) -- Barreira maior
        barrier.Position = basePosition + Vector3.new(0, 12.5, 0)
        barrier.BrickColor = BrickColor.new("Cyan")
        barrier.Material = Enum.Material.ForceField
        barrier.Transparency = 0.7
        barrier.CanCollide = false
        barrier.Shape = Enum.PartType.Cylinder
    end
    
    -- Ajusta ClaimPad
    if claimPad then
        claimPad.Size = Vector3.new(10, 1, 10) -- Maior para ser mais visível
        claimPad.Position = basePosition + Vector3.new(0, 2, 0)
        claimPad.BrickColor = BrickColor.new("Bright yellow")
        claimPad.Material = Enum.Material.Neon
        
        -- Adiciona texto ao ClaimPad
        local existingGui = claimPad:FindFirstChild("SurfaceGui")
        if existingGui then existingGui:Destroy() end
        
        local surfaceGui = Instance.new("SurfaceGui")
        surfaceGui.Face = Enum.NormalId.Top
        surfaceGui.Parent = claimPad
        
        local textLabel = Instance.new("TextLabel")
        textLabel.Size = UDim2.new(1, 0, 1, 0)
        textLabel.BackgroundTransparency = 1
        textLabel.Text = "🏠 REIVINDICAR BASE"
        textLabel.TextColor3 = Color3.new(0, 0, 0)
        textLabel.TextScaled = true
        textLabel.Font = Enum.Font.SourceSansBold
        textLabel.Parent = surfaceGui
    end
    
    -- Ajusta SpawnLocation (local de depósito)
    if spawnLocation then
        spawnLocation.Size = Vector3.new(8, 1, 8)
        spawnLocation.Position = basePosition + Vector3.new(12, 2, 0) -- Mais longe do centro
        spawnLocation.BrickColor = BrickColor.new("Bright blue")
        spawnLocation.Material = Enum.Material.ForceField
        spawnLocation.Enabled = false
        
        -- Adiciona texto ao SpawnLocation
        local existingGui = spawnLocation:FindFirstChild("SurfaceGui")
        if existingGui then existingGui:Destroy() end
        
        local surfaceGui = Instance.new("SurfaceGui")
        surfaceGui.Face = Enum.NormalId.Top
        surfaceGui.Parent = spawnLocation
        
        local textLabel = Instance.new("TextLabel")
        textLabel.Size = UDim2.new(1, 0, 1, 0)
        textLabel.BackgroundTransparency = 1
        textLabel.Text = "📦 DEPÓSITO"
        textLabel.TextColor3 = Color3.new(1, 1, 1)
        textLabel.TextScaled = true
        textLabel.Font = Enum.Font.SourceSansBold
        textLabel.Parent = surfaceGui
    end
    
    -- Garante que todos os valores necessários existem
    local values = {"Owner", "Partner", "BaseSize", "BuildingMaterials", "TeamColor"}
    for _, valueName in ipairs(values) do
        if not base:FindFirstChild(valueName) then
            local valueType = "ObjectValue"
            if valueName == "BaseSize" or valueName == "BuildingMaterials" then
                valueType = "NumberValue"
            elseif valueName == "TeamColor" then
                valueType = "StringValue"
            end
            
            local newValue = Instance.new(valueType)
            newValue.Name = valueName
            if valueName == "BaseSize" then
                newValue.Value = 100
            elseif valueName == "BuildingMaterials" then
                newValue.Value = 0
            elseif valueName == "TeamColor" then
                newValue.Value = teamColors[index] or "Bright red"
            end
            newValue.Parent = base
        end
    end
    
    print("✅ " .. base.Name .. " ajustada com sucesso!")
end

-- Encontra e ajusta todas as bases
local basesFound = 0
for i = 1, 8 do
    local baseName = "Base_" .. i
    local base = workspace:FindFirstChild(baseName)
    if base then
        adjustBase(base, i)
        basesFound = basesFound + 1
    else
        print("⚠️ " .. baseName .. " não encontrada no workspace")
    end
end

print("🎉 AJUSTE DE BASES CONCLUÍDO!")
print("📊 " .. basesFound .. " bases ajustadas")
print("🎯 Melhorias aplicadas:")
print("• Mastros mais altos (20 studs)")
print("• Bandeiras maiores e visíveis (8x6)")
print("• Barreiras ajustadas (100x25)")
print("• ClaimPads maiores com texto")
print("• SpawnLocations com texto de depósito")
print("• Todos os valores necessários criados")
print("• Bandeiras orientadas para o centro")

-- Adiciona luzes aos mastros para melhor visibilidade
for i = 1, 8 do
    local base = workspace:FindFirstChild("Base_" .. i)
    if base then
        local coreTower = base:FindFirstChild("CoreTower")
        if coreTower then
            -- Remove luz existente
            local existingLight = coreTower:FindFirstChild("PointLight")
            if existingLight then existingLight:Destroy() end
            
            -- Adiciona nova luz
            local light = Instance.new("PointLight")
            light.Color = BrickColor.new(teamColors[i] or "Bright red").Color
            light.Brightness = 2
            light.Range = 30
            light.Parent = coreTower
        end
    end
end

print("💡 Luzes adicionadas aos mastros para melhor visibilidade!")