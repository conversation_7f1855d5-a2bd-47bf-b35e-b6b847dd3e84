-- VisualEffectsManager.lua
-- Gerencia sincronização de efeitos visuais entre jogadores

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Aguarda RemoteEvents
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local showLaserEffect = remoteEvents:WaitForChild("ShowLaserEffect")
local showBeamEffect = remoteEvents:WaitForChild("ShowBeamEffect")

-- Função para mostrar laser da CombatGun para todos os jogadores
local function showLaserToAll(shooter, startPos, endPos, hitTarget)
    -- Envia para todos os jogadores exceto o atirador (que já vê localmente)
    for _, player in pairs(Players:GetPlayers()) do
        if player ~= shooter then
            showLaserEffect:FireClient(player, shooter.Name, startPos, endPos, hitTarget)
        end
    end
    
    print("🔫 Laser da CombatGun de " .. shooter.Name .. " sincronizado para todos os jogadores")
end

-- Função para mostrar beam da CollectorGun para todos os jogadores
local function showBeamToAll(collector, startPos, endPos, color, targetType)
    -- Envia para todos os jogadores exceto o coletor (que já vê localmente)
    for _, player in pairs(Players:GetPlayers()) do
        if player ~= collector then
            showBeamEffect:FireClient(player, collector.Name, startPos, endPos, color, targetType)
        end
    end
    
    print("🔨 Beam da CollectorGun de " .. collector.Name .. " sincronizado para todos os jogadores")
end

-- Conecta eventos do servidor
showLaserEffect.OnServerEvent:Connect(function(player, startPos, endPos, hitTarget)
    showLaserToAll(player, startPos, endPos, hitTarget)
end)

showBeamEffect.OnServerEvent:Connect(function(player, startPos, endPos, color, targetType)
    showBeamToAll(player, startPos, endPos, color, targetType)
end)

print("VisualEffectsManager inicializado com sucesso!")

return true
