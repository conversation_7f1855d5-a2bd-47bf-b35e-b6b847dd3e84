-- TurretManager.lua
-- Gerencia o sistema de torretas que atiram laser nos inimigos

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Debris = game:GetService("Debris")

-- Carrega configurações
local GameConfig = require(ReplicatedStorage:WaitForChild("GameConfig"))

local TurretManager = {}

-- Configurações das torretas
local TURRET_CONFIG = {
    RANGE = 50,
    DAMAGE = 15,
    FIRE_RATE = 1.5, -- Dispara a cada 1.5 segundos
    LASER_SPEED = 100,
    LASER_DURATION = 0.5
}

-- Lista de torretas ativas
local activeTurrets = {}

-- Função para verificar se um jogador é inimigo da base
local function isPlayerEnemy(player, base)
    if not player or not base then return false end
    
    local owner = base:FindFirstChild("Owner")
    local partner = base:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Partner")
    
    -- Se a base não tem dono, não atira em ninguém
    if not owner or not owner.Value then
        return false
    end
    
    -- Se o jogador é o dono ou parceiro, não é inimigo
    if (owner and owner.Value == player) or (partner and partner.Value == player) then
        return false
    end
    
    return true
end

-- Função para encontrar inimigos próximos
local function findNearbyEnemies(turret)
    local enemies = {}
    local turretPosition = turret.Position
    local base = turret:FindFirstChild("BaseOwner") and turret.BaseOwner.Value
    
    if not base then return enemies end
    
    -- Verifica jogadores
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") and player.Character:FindFirstChild("Humanoid") then
            local humanoid = player.Character.Humanoid
            if humanoid.Health > 0 and isPlayerEnemy(player, base) then
                local distance = (player.Character.HumanoidRootPart.Position - turretPosition).Magnitude
                if distance <= TURRET_CONFIG.RANGE then
                    table.insert(enemies, {
                        target = player.Character.HumanoidRootPart,
                        humanoid = humanoid,
                        distance = distance,
                        player = player
                    })
                end
            end
        end
    end
    
    -- Verifica NPCs (se houver)
    for _, npc in ipairs(workspace:GetChildren()) do
        if npc.Name:match("NPC") and npc:FindFirstChild("HumanoidRootPart") and npc:FindFirstChild("Humanoid") then
            local humanoid = npc.Humanoid
            if humanoid.Health > 0 then
                local distance = (npc.HumanoidRootPart.Position - turretPosition).Magnitude
                if distance <= TURRET_CONFIG.RANGE then
                    table.insert(enemies, {
                        target = npc.HumanoidRootPart,
                        humanoid = humanoid,
                        distance = distance,
                        npc = npc
                    })
                end
            end
        end
    end
    
    -- Ordena por distância (mais próximo primeiro)
    table.sort(enemies, function(a, b) return a.distance < b.distance end)
    
    return enemies
end

-- Função para criar efeito de laser
local function createLaserEffect(startPos, endPos, turret)
    local laser = Instance.new("Part")
    laser.Name = "TurretLaser"
    laser.Size = Vector3.new(0.3, 0.3, (endPos - startPos).Magnitude)
    laser.BrickColor = BrickColor.new("Bright red")
    laser.Material = Enum.Material.Neon
    laser.CanCollide = false
    laser.Anchored = true
    laser.Parent = workspace
    
    -- Posiciona o laser
    laser.CFrame = CFrame.lookAt(startPos, endPos) * CFrame.new(0, 0, -laser.Size.Z / 2)
    
    -- Efeito de luz
    local light = Instance.new("PointLight")
    light.Color = Color3.new(1, 0, 0)
    light.Brightness = 2
    light.Range = 10
    light.Parent = laser
    
    -- Som do laser
    local sound = Instance.new("Sound")
    sound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    sound.Volume = 0.3
    sound.Pitch = 0.8
    sound.Parent = laser
    sound:Play()
    
    -- Remove o laser após um tempo
    Debris:AddItem(laser, TURRET_CONFIG.LASER_DURATION)
    
    return laser
end

-- Função para a torreta atirar
local function turretShoot(turret, target)
    local turretPosition = turret.Position + Vector3.new(0, turret.Size.Y/2, 0)
    local targetPosition = target.target.Position
    
    -- Cria efeito de laser
    createLaserEffect(turretPosition, targetPosition, turret)
    
    -- Aplica dano
    target.humanoid.Health = math.max(0, target.humanoid.Health - TURRET_CONFIG.DAMAGE)
    
    -- Log do ataque
    if target.player then
        print("🔫 Torreta atingiu " .. target.player.Name .. " causando " .. TURRET_CONFIG.DAMAGE .. " de dano!")
    elseif target.npc then
        print("🔫 Torreta atingiu " .. target.npc.Name .. " causando " .. TURRET_CONFIG.DAMAGE .. " de dano!")
    end
    
    -- Atualiza último disparo
    turret:SetAttribute("LastShot", tick())
end

-- Função para atualizar uma torreta
local function updateTurret(turret)
    if not turret or not turret.Parent then return end
    
    local lastShot = turret:GetAttribute("LastShot") or 0
    local currentTime = tick()
    
    -- Verifica se pode atirar
    if currentTime - lastShot < TURRET_CONFIG.FIRE_RATE then
        return
    end
    
    -- Encontra inimigos próximos
    local enemies = findNearbyEnemies(turret)
    
    if #enemies > 0 then
        -- Atira no inimigo mais próximo
        turretShoot(turret, enemies[1])
    end
end

-- Função para registrar uma nova torreta
function TurretManager.RegisterTurret(turret)
    if not turret or turret.Name ~= "Turret_Building" then return end
    
    -- Adiciona à lista de torretas ativas
    table.insert(activeTurrets, turret)
    
    -- Configura atributos iniciais
    turret:SetAttribute("LastShot", 0)
    
    -- Adiciona efeitos visuais à torreta
    local light = Instance.new("PointLight")
    light.Color = Color3.new(1, 0, 0)
    light.Brightness = 0.5
    light.Range = 5
    light.Parent = turret
    
    -- Adiciona um "cano" à torreta
    local barrel = Instance.new("Part")
    barrel.Name = "Barrel"
    barrel.Size = Vector3.new(0.5, 0.5, 2)
    barrel.BrickColor = BrickColor.new("Really black")
    barrel.Material = Enum.Material.Metal
    barrel.CanCollide = false
    barrel.Anchored = true
    barrel.Parent = turret
    barrel.CFrame = turret.CFrame * CFrame.new(0, turret.Size.Y/2, 0)
    
    print("🔫 Torreta registrada e ativa!")
end

-- Função para remover torreta da lista
function TurretManager.UnregisterTurret(turret)
    for i = #activeTurrets, 1, -1 do
        if activeTurrets[i] == turret then
            table.remove(activeTurrets, i)
            break
        end
    end
end

-- Loop principal das torretas
local function turretLoop()
    while true do
        wait(0.1) -- Atualiza a cada 0.1 segundos
        
        -- Remove torretas inválidas
        for i = #activeTurrets, 1, -1 do
            local turret = activeTurrets[i]
            if not turret or not turret.Parent then
                table.remove(activeTurrets, i)
            else
                updateTurret(turret)
            end
        end
    end
end

-- Monitora criação de torretas
workspace.ChildAdded:Connect(function(child)
    if child:IsA("Part") and child.Name == "Turret_Building" then
        wait(0.1) -- Aguarda a torreta ser totalmente configurada
        TurretManager.RegisterTurret(child)
    end
end)

-- Monitora remoção de torretas
workspace.ChildRemoved:Connect(function(child)
    if child:IsA("Part") and child.Name == "Turret_Building" then
        TurretManager.UnregisterTurret(child)
    end
end)

-- Registra torretas existentes
spawn(function()
    wait(2) -- Aguarda o jogo carregar
    for _, obj in ipairs(workspace:GetChildren()) do
        if obj:IsA("Part") and obj.Name == "Turret_Building" then
            TurretManager.RegisterTurret(obj)
        end
    end
end)

-- Inicia o loop das torretas
spawn(function()
    turretLoop()
end)

print("TurretManager inicializado com sucesso!")

return TurretManager