-- SimpleCollectorScript.client.lua
-- Script simples para CollectorGun que funciona diretamente

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Configurações da ferramenta
local RANGE = 45  -- Reduzido em 70% (era 150, agora é 30% do original)
local COLLECT_RATE = 0.1
local MAX_ITEMS = 5

-- Controle de uso
local isCollecting = false
local currentTarget = nil
local collectConnection = nil
local collectingResources = {} -- Tabela para rastrear recursos sendo coletados
local progressBars = {} -- Tabela para rastrear barras de progresso
local lockedTarget = nil -- Objeto travado para coleta
local lastBeamTime = 0 -- Controle de tempo do último laser
local BEAM_COOLDOWN = 0.1 -- Cooldown entre lasers (10 FPS)
local lastPrintTime = 0 -- <PERSON>e de prints para evitar spam
local PRINT_COOLDOWN = 1 -- Cooldown entre prints (1 segundo)
local toolConnections = {} -- Tabela para rastrear conexões da ferramenta
local isToolEquipped = false -- Flag para verificar se a ferramenta está equipada

-- Sistema de inventário
local itemsCarried = 0
local originalWalkSpeed = 32  -- Dobrado de 16 para 32
local originalJumpPower = 50

-- Função para atualizar velocidade baseada no peso
local function updatePlayerSpeed()
    if not player.Character or not player.Character:FindFirstChild("Humanoid") then
        return
    end

    local humanoid = player.Character.Humanoid

    if itemsCarried == 0 then
        -- Velocidade normal
        humanoid.WalkSpeed = originalWalkSpeed
        humanoid.JumpPower = originalJumpPower
    else
        -- Reduz velocidade baseado na quantidade de itens
        local speedReduction = (itemsCarried / MAX_ITEMS) * 0.5 -- Máximo 50% de redução
        local jumpReduction = (itemsCarried / MAX_ITEMS) * 0.5 -- Máximo 50% de redução

        humanoid.WalkSpeed = originalWalkSpeed * (1 - speedReduction)
        humanoid.JumpPower = originalJumpPower * (1 - jumpReduction)
    end
end

-- Função para adicionar item ao inventário
local function addItemToInventory()
    if itemsCarried < MAX_ITEMS then
        itemsCarried = itemsCarried + 1
        updatePlayerSpeed()

        -- Atualiza UI se existir
        if _G.UpdateInventoryUI then
            _G.UpdateInventoryUI(itemsCarried, MAX_ITEMS)
        end

        -- Envia evento para o servidor para sincronizar inventário
        local ReplicatedStorage = game:GetService("ReplicatedStorage")
        local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
        if remoteEvents then
            local updateInventoryEvent = remoteEvents:FindFirstChild("UpdateInventory")
            if not updateInventoryEvent then
                updateInventoryEvent = Instance.new("RemoteEvent")
                updateInventoryEvent.Name = "UpdateInventory"
                updateInventoryEvent.Parent = remoteEvents
            end
            updateInventoryEvent:FireServer(itemsCarried)
        end

        print("💎 Item coletado! (" .. itemsCarried .. "/" .. MAX_ITEMS .. ")")
        return true
    else
        print("🎒 Inventário cheio! (" .. itemsCarried .. "/" .. MAX_ITEMS .. ")")
        return false
    end
end

-- Função para remover item do inventário
local function removeItemFromInventory()
    if itemsCarried > 0 then
        itemsCarried = itemsCarried - 1
        updatePlayerSpeed()

        -- Atualiza UI se existir
        if _G.UpdateInventoryUI then
            _G.UpdateInventoryUI(itemsCarried, MAX_ITEMS)
        end

        print("💎 Item removido! (" .. itemsCarried .. "/" .. MAX_ITEMS .. ")")
        return true
    end
    return false
end

-- Função para soltar um item no chão (via servidor)
local function dropItem()
    if not removeItemFromInventory() then
        return
    end

    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end

    -- Calcula posição na frente do jogador
    local humanoidRootPart = player.Character.HumanoidRootPart
    local dropPosition = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 3

    -- Envia evento para o servidor criar o item
    local ReplicatedStorage = game:GetService("ReplicatedStorage")
    local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
    if remoteEvents then
        local dropItemEvent = remoteEvents:FindFirstChild("DropItem")
        if dropItemEvent then
            dropItemEvent:FireServer(dropPosition)
            print("📦 Item descartado no chão!")
        end
    end
end

-- Função para criar barra de progresso
local function createProgressBar(resource)
    if progressBars[resource] then
        return progressBars[resource]
    end

    -- Cria ScreenGui para a barra
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "ProgressBarGui"
    screenGui.Parent = player.PlayerGui

    -- Frame principal da barra
    local progressFrame = Instance.new("Frame")
    progressFrame.Size = UDim2.new(0, 100, 0, 20)
    progressFrame.BackgroundColor3 = Color3.new(0, 0, 0)
    progressFrame.BackgroundTransparency = 0.3
    progressFrame.BorderSizePixel = 2
    progressFrame.BorderColor3 = Color3.new(1, 1, 1)
    progressFrame.Parent = screenGui

    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 6)
    corner.Parent = progressFrame

    -- Barra de progresso interna
    local progressBar = Instance.new("Frame")
    progressBar.Name = "ProgressBar"
    progressBar.Size = UDim2.new(1, 0, 1, 0)
    progressBar.Position = UDim2.new(0, 0, 0, 0)
    progressBar.BackgroundColor3 = Color3.new(0, 0.8, 1)
    progressBar.BorderSizePixel = 0
    progressBar.Parent = progressFrame

    local progressCorner = Instance.new("UICorner")
    progressCorner.CornerRadius = UDim.new(0, 6)
    progressCorner.Parent = progressBar

    -- Texto da barra
    local progressText = Instance.new("TextLabel")
    progressText.Size = UDim2.new(1, 0, 1, 0)
    progressText.Position = UDim2.new(0, 0, 0, 0)
    progressText.BackgroundTransparency = 1
    progressText.Text = "Coletando..."
    progressText.TextColor3 = Color3.new(1, 1, 1)
    progressText.TextScaled = true
    progressText.Font = Enum.Font.GothamBold
    progressText.Parent = progressFrame

    progressBars[resource] = {
        gui = screenGui,
        frame = progressFrame,
        bar = progressBar,
        text = progressText
    }

    return progressBars[resource]
end

-- Função para atualizar posição da barra de progresso
local function updateProgressBarPosition(resource, progressBarData)
    if not resource or not resource.Parent or not progressBarData then
        return
    end

    local camera = workspace.CurrentCamera
    local resourcePosition = resource.Position + Vector3.new(0, resource.Size.Y/2 + 2, 0)
    local screenPosition, onScreen = camera:WorldToScreenPoint(resourcePosition)

    if onScreen then
        progressBarData.frame.Position = UDim2.new(0, screenPosition.X - 50, 0, screenPosition.Y - 10)
        progressBarData.frame.Visible = true
    else
        progressBarData.frame.Visible = false
    end
end

-- Função para atualizar barra de progresso
local function updateProgressBar(resource, progress, isGrowing)
    local progressBarData = progressBars[resource]
    if not progressBarData then
        return
    end

    -- Atualiza tamanho da barra
    progressBarData.bar.Size = UDim2.new(progress, 0, 1, 0)

    -- Muda cor baseado no estado
    if isGrowing then
        progressBarData.bar.BackgroundColor3 = Color3.new(0.2, 1, 0.2) -- Verde para crescimento
        progressBarData.text.Text = "Regenerando..."
    else
        progressBarData.bar.BackgroundColor3 = Color3.new(1, 0.5, 0) -- Laranja para coleta
        progressBarData.text.Text = "Coletando..."
    end

    -- Atualiza posição
    updateProgressBarPosition(resource, progressBarData)
end

-- Função para remover barra de progresso
local function removeProgressBar(resource)
    local progressBarData = progressBars[resource]
    if progressBarData then
        progressBarData.gui:Destroy()
        progressBars[resource] = nil
    end
end

-- Função para criar efeito visual do raio (com cooldown)
local function createBeamEffect(startPos, endPos, color)
    local currentTime = tick()
    
    -- Verifica cooldown
    if currentTime - lastBeamTime < BEAM_COOLDOWN then
        return nil
    end
    
    lastBeamTime = currentTime
    
    local beam = Instance.new("Part")
    beam.Name = "CollectorBeam"
    beam.Size = Vector3.new(0.2, 0.2, (endPos - startPos).Magnitude)
    beam.BrickColor = color or BrickColor.new("Bright blue")
    beam.Material = Enum.Material.Neon
    beam.CanCollide = false
    beam.Anchored = true
    beam.Parent = workspace
    
    -- Posiciona o raio
    beam.CFrame = CFrame.lookAt(startPos, endPos) * CFrame.new(0, 0, -beam.Size.Z / 2)
    
    -- Efeito de fade mais rápido
    local transparency = 0
    local connection
    connection = RunService.Heartbeat:Connect(function()
        transparency = transparency + 0.15 -- Mais rápido para evitar acúmulo
        beam.Transparency = transparency
        
        if transparency >= 1 then
            connection:Disconnect()
            beam:Destroy()
        end
    end)
    
    return beam
end

-- Função para verificar se um objeto é coletável
local function isCollectable(obj)
    return obj:FindFirstChild("OriginalSize") and obj.Parent == workspace
end

-- Função para criar efeitos visuais de coleta
local function createCollectionEffects(resource)
    if not resource or not resource.Parent then return end

    local resourcePos = resource.Position
    local resourceColor = resource.BrickColor

    -- Efeito de partículas voando em direção ao jogador
    for i = 1, 8 do
        local particle = Instance.new("Part")
        particle.Name = "CollectionParticle"
        particle.Size = Vector3.new(0.3, 0.3, 0.3)
        particle.Position = resourcePos + Vector3.new(
            math.random(-2, 2),
            math.random(-1, 1),
            math.random(-2, 2)
        )
        particle.BrickColor = resourceColor
        particle.Material = Enum.Material.Neon
        particle.CanCollide = false
        particle.Anchored = false
        particle.Shape = Enum.PartType.Ball
        particle.Parent = workspace

        -- Aplica força em direção ao jogador
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local bodyVelocity = Instance.new("BodyVelocity")
            bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
            local direction = (player.Character.HumanoidRootPart.Position - resourcePos).Unit
            bodyVelocity.Velocity = direction * 20 + Vector3.new(
                math.random(-5, 5),
                math.random(5, 15),
                math.random(-5, 5)
            )
            bodyVelocity.Parent = particle
        end

        -- Remove partícula após 1 segundo
        game:GetService("Debris"):AddItem(particle, 1)
    end

    -- Efeito de luz brilhante
    local light = Instance.new("PointLight")
    light.Brightness = 3
    light.Range = 15
    light.Color = resourceColor.Color
    light.Parent = resource

    -- Efeito de explosão de luz
    local explosion = Instance.new("Explosion")
    explosion.Position = resourcePos
    explosion.BlastRadius = 5
    explosion.BlastPressure = 0
    explosion.Visible = false
    explosion.Parent = workspace

    -- Som de coleta
    local sound = Instance.new("Sound")
    sound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
    sound.Volume = 0.5
    sound.Pitch = 1.2
    sound.Parent = resource
    sound:Play()

    -- Remove som após tocar
    sound.Ended:Connect(function()
        sound:Destroy()
    end)
end

-- Função para regenerar recursos (melhorada)
local function regenerateResources()
    for resource, originalSize in pairs(collectingResources) do
        if resource and resource.Parent then
            local currentSize = resource.Size
            local targetSize = originalSize

            -- Cresce gradualmente de volta ao tamanho original
            local growRate = 0.12 -- Regeneração mais rápida que o original (era 0.05, agora 0.12)
            local newSize = Vector3.new(
                math.min(targetSize.X, currentSize.X + growRate),
                math.min(targetSize.Y, currentSize.Y + growRate),
                math.min(targetSize.Z, currentSize.Z + growRate)
            )

            resource.Size = newSize

            -- Atualiza barra de progresso (crescimento)
            local progress = newSize.Magnitude / targetSize.Magnitude
            updateProgressBar(resource, progress, true)

            -- Remove da lista quando volta ao tamanho original
            if newSize.Magnitude >= targetSize.Magnitude * 0.98 then
                collectingResources[resource] = nil
                removeProgressBar(resource)
                print("💎 Recurso " .. resource.Name .. " regenerado completamente!")
            end
        else
            -- Remove recursos inválidos
            collectingResources[resource] = nil
            removeProgressBar(resource)
        end
    end
end

-- Variável para controlar notificação única
local inventoryFullNotificationActive = false

-- Função para mostrar notificação de inventário cheio (melhorada)
local function showInventoryFullNotification(resource)
    if not resource or not resource.Parent then return end
    
    -- Evita múltiplas notificações
    if inventoryFullNotificationActive then return end
    inventoryFullNotificationActive = true
    
    -- Cria GUI de notificação temporária
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "InventoryFullNotification"
    screenGui.Parent = player.PlayerGui
    
    -- Frame da notificação (50% menor)
    local notificationFrame = Instance.new("Frame")
    notificationFrame.Size = UDim2.new(0, 100, 0, 25) -- Reduzido de 200x50 para 100x25
    notificationFrame.BackgroundColor3 = Color3.new(1, 0, 0)
    notificationFrame.BackgroundTransparency = 0.2
    notificationFrame.BorderSizePixel = 2
    notificationFrame.BorderColor3 = Color3.new(1, 1, 1)
    notificationFrame.Parent = screenGui
    
    -- Cantos arredondados
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 6)
    corner.Parent = notificationFrame
    
    -- Texto da notificação
    local notificationText = Instance.new("TextLabel")
    notificationText.Size = UDim2.new(1, 0, 1, 0)
    notificationText.BackgroundTransparency = 1
    notificationText.Text = "🎒 CHEIO!"
    notificationText.TextColor3 = Color3.new(1, 1, 1)
    notificationText.TextScaled = true
    notificationText.Font = Enum.Font.SourceSansBold
    notificationText.Parent = notificationFrame
    
    -- Função para atualizar posição seguindo o objeto
    local function updatePosition()
        if resource and resource.Parent then
            local camera = workspace.CurrentCamera
            local objectPosition = resource.Position + Vector3.new(0, resource.Size.Y/2 + 2, 0)
            local screenPosition, onScreen = camera:WorldToScreenPoint(objectPosition)
            
            if onScreen then
                notificationFrame.Position = UDim2.new(0, screenPosition.X - 50, 0, screenPosition.Y - 12.5)
                notificationFrame.Visible = true
            else
                notificationFrame.Visible = false
            end
        end
    end
    
    -- Atualiza posição continuamente
    local updateConnection = RunService.Heartbeat:Connect(updatePosition)
    
    -- Remove após 2 segundos
    spawn(function()
        wait(2)
        inventoryFullNotificationActive = false
        if updateConnection then
            updateConnection:Disconnect()
        end
        if screenGui and screenGui.Parent then
            screenGui:Destroy()
        end
    end)
end

-- Função para coletar recurso (versão melhorada)
local function collectResource(resource)
    if not resource or not resource.Parent then return end

    -- Verifica se pode coletar mais itens
    if itemsCarried >= MAX_ITEMS then
        showInventoryFullNotification(resource)
        print("🎒 Inventário cheio! Não é possível coletar mais itens.")
        return false -- Retorna false para parar o loop
    end

    -- Salva tamanho original se não estiver salvo
    if not collectingResources[resource] then
        local originalSizeValue = resource:FindFirstChild("OriginalSize")
        if originalSizeValue then
            collectingResources[resource] = originalSizeValue.Value
        else
            collectingResources[resource] = resource.Size
        end

        -- Cria barra de progresso
        createProgressBar(resource)
        print("💎 Iniciando coleta de " .. resource.Name)
    end

    local originalSize = collectingResources[resource]

    -- Encolhe o recurso mais lentamente
    local currentSize = resource.Size
    local shrinkRate = 0.08 -- Voltando para velocidade original de coleta
    local newSize = Vector3.new(
        math.max(0.1, currentSize.X - shrinkRate),
        math.max(0.1, currentSize.Y - shrinkRate),
        math.max(0.1, currentSize.Z - shrinkRate)
    )

    if newSize.X <= 0.3 or newSize.Y <= 0.3 or newSize.Z <= 0.3 then
        -- Recurso coletado completamente
        collectingResources[resource] = nil -- Remove da lista de regeneração
        removeProgressBar(resource)

        -- Cria efeitos visuais de coleta
        createCollectionEffects(resource)

        -- Se este era o alvo travado, destravar
        if lockedTarget == resource then
            lockedTarget = nil
            print("🎯 Alvo coletado - destravando")
        end

        resource:Destroy()

        -- Adiciona ao inventário
        local success = addItemToInventory()
        if success then
            print("💎 Recurso coletado com sucesso! Inventário: " .. itemsCarried .. "/" .. MAX_ITEMS)
        else
            print("❌ Erro ao adicionar item ao inventário!")
        end

    else
        -- Continua encolhendo
        resource.Size = newSize

        -- Atualiza barra de progresso (coleta)
        local progress = 1 - (newSize.Magnitude / originalSize.Magnitude)
        updateProgressBar(resource, progress, false)
    end
end

-- Função para verificar se um objeto é uma base inimiga
local function isEnemyBase(obj)
    local baseModel = obj.Parent
    if baseModel and baseModel:FindFirstChild("Owner") and baseModel:FindFirstChild("BaseSize") then
        local owner = baseModel.Owner.Value
        return owner and owner ~= player
    end
    return false
end

-- Função para atacar base (via servidor)
local function attackBase(baseModel)
    if not baseModel then return end
    
    -- Envia ataque para o servidor
    local ReplicatedStorage = game:GetService("ReplicatedStorage")
    local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
    if remoteEvents then
        local attackBaseEvent = remoteEvents:FindFirstChild("AttackBase")
        if not attackBaseEvent then
            attackBaseEvent = Instance.new("RemoteEvent")
            attackBaseEvent.Name = "AttackBase"
            attackBaseEvent.Parent = remoteEvents
        end
        
        attackBaseEvent:FireServer(baseModel)
        print("💥 " .. player.Name .. " atacou " .. baseModel.Name .. "!")
    end
end

-- Função para iniciar coleta/ataque
local function startCollectingResource()
    isCollecting = true
    lockedTarget = nil -- Reset do alvo travado
    
    collectConnection = RunService.Heartbeat:Connect(function()
        if not isCollecting then
            if collectConnection then
                collectConnection:Disconnect()
                collectConnection = nil
            end
            return
        end
        
        -- Raycast para detectar alvo
        local character = player.Character
        if not character then return end
        
        local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
        if not humanoidRootPart then return end
        
        -- Posição de origem
        local startPos = humanoidRootPart.Position + humanoidRootPart.CFrame.LookVector * 2
        local direction = (mouse.Hit.Position - startPos).Unit
        
        -- Se já tem um alvo travado, continua com ele
        if lockedTarget and lockedTarget.Parent then
            local distance = (lockedTarget.Position - startPos).Magnitude
            if distance <= RANGE then
                -- Continua coletando o alvo travado
                local endPos = lockedTarget.Position
                
                -- Cria efeito visual
                createBeamEffect(startPos, endPos, BrickColor.new("Bright blue"))
                
                -- Coleta o recurso (para se inventário cheio)
                local canCollect = collectResource(lockedTarget)
                if canCollect == false then
                    -- Para o loop se inventário cheio
                    isCollecting = false
                    lockedTarget = nil
                    if collectConnection then
                        collectConnection:Disconnect()
                        collectConnection = nil
                    end
                    return
                end
                
                currentTarget = lockedTarget
                return
            else
                -- Alvo muito longe, destravar
                lockedTarget = nil
            end
        end
        
        -- Raycast para encontrar novo alvo
        local raycastParams = RaycastParams.new()
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
        raycastParams.FilterDescendantsInstances = {character}
        
        local raycastResult = workspace:Raycast(startPos, direction * RANGE, raycastParams)
        
        if raycastResult then
            local hitObject = raycastResult.Instance
            local endPos = raycastResult.Position
            
            -- Verifica se é um recurso coletável
            if isCollectable(hitObject) then
                -- Só processa se for o alvo travado ou se não há alvo travado
                if not lockedTarget or lockedTarget == hitObject then
                    -- Trava no primeiro objeto encontrado
                    if not lockedTarget then
                        lockedTarget = hitObject
                        local currentTime = tick()
                        if currentTime - lastPrintTime > PRINT_COOLDOWN then
                            print("🎯 Alvo travado: " .. hitObject.Name)
                            lastPrintTime = currentTime
                        end
                    end
                    
                    currentTarget = hitObject
                    
                    -- Cria efeito visual
                    createBeamEffect(startPos, endPos, BrickColor.new("Bright blue"))
                    
                    -- Coleta o recurso (para se inventário cheio)
                    local canCollect = collectResource(hitObject)
                    if canCollect == false then
                        -- Para o loop se inventário cheio
                        isCollecting = false
                        lockedTarget = nil
                        if collectConnection then
                            collectConnection:Disconnect()
                            collectConnection = nil
                        end
                        return
                    end
                end
                
            -- Verifica se é uma base inimiga
            elseif isEnemyBase(hitObject) then
                currentTarget = hitObject
                
                -- Cria efeito visual vermelho para ataque
                createBeamEffect(startPos, endPos, BrickColor.new("Bright red"))
                
                -- Ataca a base
                attackBase(hitObject.Parent)
                
            else
                currentTarget = nil
            end
        else
            currentTarget = nil
        end
    end)
end

-- Função para parar coleta/ataque
local function stopCollectingResource()
    isCollecting = false
    lockedTarget = nil -- Reset do alvo travado

    if collectConnection then
        collectConnection:Disconnect()
        collectConnection = nil
    end

    currentTarget = nil
    print("🔄 Coleta parada - alvo destravado")

    -- Inicia regeneração dos recursos que estavam sendo coletados
    if next(collectingResources) then
        spawn(function()
            while next(collectingResources) do
                regenerateResources()
                wait(0.1) -- Atualiza a cada 0.1 segundos
            end
        end)
    end
end

-- Função para limpar conexões antigas
local function clearToolConnections()
    for _, connection in pairs(toolConnections) do
        if connection then
            connection:Disconnect()
        end
    end
    toolConnections = {}
end

-- Função para equipar ferramenta
local function equipTool(tool)
    if isToolEquipped then return end -- Evita múltiplas equipagens
    
    isToolEquipped = true
    clearToolConnections() -- Limpa conexões antigas
    
    print("🔨 CollectorGun equipada!")
    mouse.Icon = "rbxasset://textures/ArrowCursor.png"
    
    -- Cria novas conexões
    toolConnections.activated = tool.Activated:Connect(startCollectingResource)
    toolConnections.deactivated = tool.Deactivated:Connect(stopCollectingResource)
    toolConnections.unequipped = tool.Unequipped:Connect(function()
        unequipTool()
    end)
end

-- Função para desequipar ferramenta
local function unequipTool()
    if not isToolEquipped then return end -- Evita múltiplas desequipagens
    
    isToolEquipped = false
    stopCollectingResource()
    clearToolConnections()
    mouse.Icon = ""
    print("🔨 CollectorGun desequipada!")
end

-- Monitora quando o jogador equipa a CollectorGun
local function monitorCollectorGun()
    local character = player.Character or player.CharacterAdded:Wait()
    
    character.ChildAdded:Connect(function(child)
        if child.Name == "CollectorGun" and child:IsA("Tool") then
            equipTool(child)
        end
    end)
    
    character.ChildRemoved:Connect(function(child)
        if child.Name == "CollectorGun" and child:IsA("Tool") then
            unequipTool()
        end
    end)
    
    -- Verifica se já tem a ferramenta equipada
    local existingTool = character:FindFirstChild("CollectorGun")
    if existingTool and existingTool:IsA("Tool") then
        equipTool(existingTool)
    end
end

-- Inicializa o monitoramento
if player.Character then
    monitorCollectorGun()
end

player.CharacterAdded:Connect(function()
    wait(1)
    monitorCollectorGun()
end)

-- Função para encontrar a base do jogador
local function getPlayerBase()
    for _, base in ipairs(workspace:GetChildren()) do
        if base.Name:match("Base_") then
            local owner = base:FindFirstChild("Owner")
            local partner = base:FindFirstChild("Partner")

            if owner and (owner.Value == player or (partner and partner.Value == player)) then
                return base
            end
        end
    end
    return nil
end

-- Função para verificar se jogador está no bloco de depósito
local function isPlayerOnDepositBlock(base)
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return false
    end

    local spawnLocation = base:FindFirstChild("SpawnLocation")
    if not spawnLocation then return false end

    local playerPos = player.Character.HumanoidRootPart.Position
    local blockPos = spawnLocation.Position
    local blockSize = spawnLocation.Size

    -- Verifica se o jogador está em cima do bloco (com margem)
    local xInRange = math.abs(playerPos.X - blockPos.X) <= (blockSize.X / 2 + 2)
    local zInRange = math.abs(playerPos.Z - blockPos.Z) <= (blockSize.Z / 2 + 2)
    local yInRange = playerPos.Y >= (blockPos.Y + blockSize.Y / 2 - 1) and
                     playerPos.Y <= (blockPos.Y + blockSize.Y / 2 + 10)

    return xInRange and zInRange and yInRange
end

-- Função para depositar todos os itens
local function depositAllItems()
    if itemsCarried <= 0 then
        return false
    end

    local base = getPlayerBase()
    if not base then
        print("❌ Você não possui uma base!")
        return false
    end

    if not isPlayerOnDepositBlock(base) then
        print("❌ Você precisa estar em cima do bloco azul da sua base!")
        return false
    end

    -- Deposita todos os itens
    local depositedItems = itemsCarried
    itemsCarried = 0
    updatePlayerSpeed()

    -- Atualiza UI se existir
    if _G.UpdateInventoryUI then
        _G.UpdateInventoryUI(itemsCarried, MAX_ITEMS)
    end

    print("💰 " .. depositedItems .. " itens depositados na base!")
    return true
end

-- Loop para verificar depósito automático
spawn(function()
    while true do
        wait(0.5) -- Verifica a cada meio segundo

        if itemsCarried > 0 and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local base = getPlayerBase()
            if base and isPlayerOnDepositBlock(base) then
                depositAllItems()
            end
        end
    end
end)

-- Sistema de regeneração contínua independente
spawn(function()
    while true do
        wait(0.1)
        if next(collectingResources) then
            regenerateResources()
        end
    end
end)

-- Conecta tecla para soltar itens (X)
local UserInputService = game:GetService("UserInputService")
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.X then
        dropItem()
    elseif input.KeyCode == Enum.KeyCode.R then
        -- Tecla R para depositar manualmente (mudado de E para R)
        depositAllItems()
    end
end)

-- Conecta evento de coleta de item dropado
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local collectDroppedItemEvent = remoteEvents:WaitForChild("CollectDroppedItem")

collectDroppedItemEvent.OnClientEvent:Connect(function()
    addItemToInventory()
    print("💎 Item dropado coletado!")
end)

-- Exporta funções globais para UI
_G.DropItem = dropItem
_G.GetItemsCarried = function() return itemsCarried end
_G.GetMaxItems = function() return MAX_ITEMS end
_G.DepositAllItems = depositAllItems

print("✅ SimpleCollectorScript carregado!")