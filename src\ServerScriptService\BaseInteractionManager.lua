-- BaseInteractionManager.lua
-- Gerencia a criação de botões de interação nas bases

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local BaseInteractionManager = {}

-- Função para criar botão de interação em uma base
local function createInteractionButton(base)
    if not base or base:FindFirstChild("InteractionButton") then
        return -- Já existe ou base inválida
    end

    local basePlatform = base:FindFirstChild("BasePlatform")
    if not basePlatform then
        return
    end

    -- Cria o botão de interação
    local interactionButton = Instance.new("Part")
    interactionButton.Name = "InteractionButton"
    interactionButton.Size = Vector3.new(3, 1, 3)
    interactionButton.Position = basePlatform.Position + Vector3.new(0, basePlatform.Size.Y/2 + 0.5, 0)
    interactionButton.BrickColor = BrickColor.new("Bright blue")
    interactionButton.Material = Enum.Material.Neon
    interactionButton.CanCollide = false
    interactionButton.Anchored = true
    interactionButton.Shape = Enum.PartType.Cylinder
    interactionButton.Parent = base

    -- Adiciona efeito de luz
    local pointLight = Instance.new("PointLight")
    pointLight.Color = Color3.new(0, 0.5, 1)
    pointLight.Brightness = 2
    pointLight.Range = 10
    pointLight.Parent = interactionButton

    -- Adiciona texto 3D
    local surfaceGui = Instance.new("SurfaceGui")
    surfaceGui.Face = Enum.NormalId.Top
    surfaceGui.Parent = interactionButton

    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "🔨 CONSTRUIR"
    textLabel.TextColor3 = Color3.new(1, 1, 1)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.TextStrokeTransparency = 0
    textLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
    textLabel.Parent = surfaceGui

    -- Efeito de pulsação
    spawn(function()
        while interactionButton and interactionButton.Parent do
            for i = 1, 20 do
                if interactionButton and interactionButton.Parent then
                    interactionButton.Transparency = i / 40 -- Varia de 0 a 0.5
                    wait(0.05)
                end
            end
            for i = 20, 1, -1 do
                if interactionButton and interactionButton.Parent then
                    interactionButton.Transparency = i / 40
                    wait(0.05)
                end
            end
        end
    end)

    print("🔨 Botão de interação criado para " .. base.Name)
end

-- Função para verificar e criar botões em todas as bases
function BaseInteractionManager.UpdateAllBases()
    for i = 1, 8 do
        local base = workspace:FindFirstChild("Base_" .. i)
        if base then
            createInteractionButton(base)
        end
    end
end

-- Função para criar botão quando uma base é reivindicada
function BaseInteractionManager.OnBaseClaimed(base)
    if base then
        createInteractionButton(base)
    end
end

-- Inicializa botões para bases existentes
BaseInteractionManager.UpdateAllBases()

-- Monitora novas bases (caso sejam criadas dinamicamente)
workspace.ChildAdded:Connect(function(child)
    if child.Name:match("Base_") then
        wait(1) -- Aguarda a base ser totalmente configurada
        createInteractionButton(child)
    end
end)

print("BaseInteractionManager inicializado com sucesso!")

return BaseInteractionManager